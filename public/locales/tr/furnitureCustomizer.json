{"furnitureDesignTool": "Mobilya Tasarım Aracı", "customizeYourFurniture": "Mobilyanızı Özelleştirin", "furnitureType": "Mobilya Tipi", "armchair": "Koltuk", "bergere": "<PERSON><PERSON><PERSON>", "frame": "<PERSON><PERSON>", "baseFrame": "Alt Kasa", "woodVeneer": "Ahşap Ka<PERSON>lama", "legs": "Ayaklar", "comfort": "Koltuk", "armrest": "<PERSON><PERSON>", "seatingArea": "O<PERSON><PERSON>", "model": "Model", "fabrics": "<PERSON><PERSON><PERSON><PERSON>", "sharedFabric": "Kumaş", "sharedFabricArmchair": "(Alt Kasa, Kol, Oturum)", "sharedFabricBergere": "(<PERSON><PERSON><PERSON>)", "woodVeneerFabric": "Ahşap Kaplama Kumaşı", "legFabric": "Ayak <PERSON>", "saveDesign": "Tasarımı Kaydet", "addToCart": "Sepete Ekle", "papel": "Papel", "kulak": "<PERSON><PERSON>", "ön klapa": "<PERSON><PERSON>", "fabric": "Kumaş", "selectCatalog": "<PERSON><PERSON><PERSON>", "skeletonColor": "İskelet Rengi", "selectSkeletonColor": "İskelet Rengi Seç", "price": "<PERSON><PERSON><PERSON>", "loading": "Yükleniyor...", "skeletonOptions": "İskelet Seçenekleri", "skeleton": "İskelet", "woodColor": "Ahşap <PERSON>", "selectWoodColor": "Ahşap Rengi Seç", "handleModelLoading": "3D Model Yükleniyor...", "brown": "Kahverengi", "greenCotton": "<PERSON><PERSON><PERSON>", "grey": "<PERSON><PERSON>", "mainAhsapFabric": "Kol Genişletme Ku<PERSON>şı", "cartela1": "Kartela 1", "armchairType": "Koltuk Tipi", "backPillow": "<PERSON><PERSON><PERSON>", "cushion1": "Kırlenti 1", "cushion2": "Kırlenti 2", "cushion3": "Kırlenti 3", "orderDetails": "Sipariş Detayları", "date": "<PERSON><PERSON><PERSON>", "customerName": "Müşteri Adı", "customerSurname": "Müşteri Soyadı", "part": "Parça", "selection": "<PERSON><PERSON><PERSON>", "totalPrice": "Toplam Fiyat", "currency": "TL", "print": "Yazdır", "barcode": "Barkod", "color": "Renk", "selectFabric": "Ku<PERSON><PERSON>", "selectColor": "Renk Seç", "selectArmrest": "<PERSON><PERSON>", "selectSeat": "Oturum Seç", "seatSubOptions": "Otur<PERSON> Seçenekleri", "seatDisabled": "Oturum devre dışı", "selectBaseFrame": "Alt Kasa Seç", "selectLegs": "Ayak Seç", "additionalOptions": "Ek <PERSON>r", "updateSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> başarılı", "createSuccess": "Oluşturma başarılı", "updateCreateError": "Güncelleme/oluşturma hatası", "fetchPricingError": "Fiyatlandırma verisi alı<PERSON>adı", "selectBackPillow": "<PERSON><PERSON><PERSON>ı<PERSON>", "cushions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectModel": "<PERSON> <PERSON><PERSON>", "3dPreview": "3D Önizleme", "3dPreviewNotAvailable": "3D önizleme mevcut değil", "basicDetails": "<PERSON><PERSON>", "designDetails": "Ta<PERSON>ım <PERSON>", "fabricDetails": "Kumaş Detayları", "cushionDetails": "<PERSON><PERSON><PERSON><PERSON>", "seat": "O<PERSON><PERSON>", "seatFabric": "O<PERSON><PERSON> Ku<PERSON>şı", "Silikon Elyaf": "Silikon Elyaf", "Sünger": "<PERSON><PERSON><PERSON>", "Yok": "Yok", "50*50": "50*50", "60*60": "60*60", "60*45": "60*45", "4lü Koltuk 270 * 100": "4'<PERSON><PERSON> 270 * 100", "3lü Koltuk 230 * 100": "3'<PERSON><PERSON> 230 * 100", "2li Koltuk 190 * 100": "2'<PERSON><PERSON> 190 * 100", "KOL 1": "KOL 1", "KOL 2": "KOL 2", "KOL 3": "KOL 3", "KOL 4": "KOL 4", "KOL 5": "KOL 5", "KOL 6": "KOL 6", "KOL 7": "KOL 7", "BOŞ": "BOŞ", "CevizAhsap": "<PERSON><PERSON><PERSON>", "BeyazAhsap": "<PERSON><PERSON>", "EkruAhsap": "Ekru", "GriAhsap": "<PERSON><PERSON>", "AntrasitAhsap": "Antrasit", "SariEskitmeAhsap": "Sarı Eskitme", "GriEskitmeAhsap": "Gri <PERSON>", "CevizEskitmeAhsap": "<PERSON><PERSON><PERSON>", "Bronz": "Bronz", "Gold": "Gold", "Nickel": "Nikel", "woodenColors": "<PERSON><PERSON><PERSON>", "legColors": "<PERSON><PERSON><PERSON>", "çektirme": "Çektirme", "tek parça": "Tek Parça", "bombe": "Bombe", "seatSubOption1": "Oturum Seçeneği 1", "seatSubOption2": "Oturum Seçeneği 2", "seatSubOption3": "Oturum Seçeneği 3", "barcodeExplanation": "Barkod Açıklaması", "fabricCode": "Kumaş <PERSON>", "fabricCodeDesc": "Kumaş tipi ve renk numarası", "armrestCode": "<PERSON><PERSON>", "armrestCodeDesc": "Kol tipi ve ek parçalar", "seatCode": "<PERSON><PERSON><PERSON> Kodu", "seatCodeDesc": "Oturum tipi ve ek parçalar", "baseFrameCode": "Alt Kasa", "baseFrameCodeDesc": "Alt kasa ve ahşap kaplama numarası", "legColorCode": "<PERSON><PERSON><PERSON>", "legColorCodeDesc": "Ayak renk indeksi", "woodColorCode": "Ahşap <PERSON>", "woodColorCodeDesc": "Ahşap renk indeksi", "bergereNotImplemented": "<PERSON><PERSON>i henüz uygulanmadı", "düğmeKapiton": "<PERSON><PERSON><PERSON><PERSON>", "boğumKapiton": "<PERSON><PERSON><PERSON>", "kırışıklık": "Kırışıklık", "enterFullscreen": "<PERSON>", "decorativeCushions": "<PERSON><PERSON><PERSON><PERSON>", "fillMaterial": "<PERSON><PERSON><PERSON>", "fabricCatalog": "Ku<PERSON>ş <PERSON>ı", "quantity": "<PERSON><PERSON>", "cushionSize": "<PERSON><PERSON><PERSON><PERSON>", "designSaved": "Ta<PERSON><PERSON>m başarıyla kaydedildi!", "designSaveFailed": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "addedToCart": "Sepete eklendi!", "addToCartFailed": "Sepete eklenemedi", "arm": "<PERSON><PERSON>", "armOptions": "<PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON><PERSON>", "joint": "<PERSON><PERSON><PERSON>", "frontFlap": "<PERSON><PERSON>", "skeletonAndSeat": "İskelet ve Oturum", "selectType": "<PERSON><PERSON>", "selectLegType": "Ayak Seç", "selectLegColor": "Renk Seç", "pull": "Çektirme", "singlePiece": "Tek Parça", "convex": "Bombe", "wrinkle": "Kırışıklık", "designAndPriceDetails": "Tasarım ve Fiyat Detayları", "customer": "Müş<PERSON>i", "product": "<PERSON><PERSON><PERSON><PERSON>", "backPillowLabel": "<PERSON><PERSON><PERSON>", "pillowLabel": "<PERSON><PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON>", "color1": "Renk 1", "color2": "Renk 2", "color3": "Renk 3", "color4": "Renk 4", "color5": "Renk 5", "color6": "Renk 6", "color7": "Renk 7", "color8": "Renk 8", "color9": "Renk 9", "fillMaterialLabel": "<PERSON><PERSON><PERSON>:", "fabricCatalogLabel": "Kumaş Kartelası:", "colorLabel": "Renk:", "quantityLabel": "Adet:", "backPillowTitle": "<PERSON><PERSON><PERSON>", "decorativePillowTitle": "<PERSON><PERSON><PERSON><PERSON> {{number}}", "sizeInCm": "Boyut (cm):", "colorOption": "Renk {{number}}", "sizeOption": "{{width}}x{{height}} cm", "frameType": "Alt Kasa", "selectFrameType": "Alt Kasa Seç", "frameType1": "Alt Kasa 1", "frameType2": "Alt Kasa 2", "frameType3": "Alt Kasa 3", "pleaseEnterCustomerName": "Lütfen müşteri adı ve soyadı girin"}