{"furnitureDesignTool": "Furniture Design Tool", "customizeYourFurniture": "Customize Your Furniture", "furnitureType": "Furniture Type", "armchair": "Armchair", "bergere": "<PERSON><PERSON>", "frame": "<PERSON>ame", "baseFrame": "Base Frame", "woodVeneer": "<PERSON>", "legs": "Legs", "comfort": "Comfort", "armrest": "Armrest", "seatingArea": "Seating Area", "model": "Model", "fabrics": "Fabrics", "sharedFabric": "<PERSON><PERSON><PERSON>", "sharedFabricArmchair": "(Base Frame, Armrest, Seat)", "sharedFabricBergere": "(<PERSON> Berger<PERSON>)", "woodVeneerFabric": "<PERSON>", "legFabric": "<PERSON><PERSON>", "saveDesign": "Save Design", "addToCart": "Add to Cart", "papel": "Papel", "kulak": "<PERSON><PERSON>", "ön klapa": "Front Flap", "brown": "<PERSON>", "greenCotton": "Green Cotton", "grey": "Grey", "mainAhsapFabric": "Armrest Extension Fabric", "cartela1": "Catalog 1", "armchairType": "Armchair Type", "backPillow": "Back Pillow", "cushion1": "Cushion 1", "cushion2": "Cushion 2", "cushion3": "Cushion 3", "orderDetails": "Order Details", "date": "Date", "customerName": "Customer Name", "customerSurname": "Customer Surname", "part": "Part", "selection": "Selection", "totalPrice": "Total Price", "currency": "TL", "print": "Print", "barcode": "Barcode", "color": "Color", "selectFabric": "Select Fabric", "selectColor": "Select Color", "selectArmrest": "Select Armrest", "selectSeat": "Select Seat", "seatSubOptions": "Seat Options", "seatDisabled": "Seat disabled", "selectBaseFrame": "Select Base Frame", "selectLegs": "Select Legs", "additionalOptions": "Additional Options", "updateSuccess": "Update successful", "createSuccess": "Creation successful", "updateCreateError": "Update/creation error", "fetchPricingError": "Failed to fetch pricing data", "selectBackPillow": "Select Back Pillow", "cushions": "Cushions", "selectModel": "Select Model", "3dPreview": "3D Preview", "3dPreviewNotAvailable": "3D preview not available", "basicDetails": "Basic Details", "designDetails": "Design Details", "fabricDetails": "<PERSON><PERSON><PERSON>", "cushionDetails": "Cushion Details", "seat": "<PERSON><PERSON>", "seatFabric": "<PERSON><PERSON>", "Silikon Elyaf": "Silicone Fiber", "Sünger": "Foam", "Yok": "None", "50*50": "50*50", "60*60": "60*60", "60*45": "60*45", "4lü Koltuk 270 * 100": "4-<PERSON><PERSON> 270 * 100", "3lü Koltuk 230 * 100": "3-<PERSON><PERSON> 230 * 100", "2li Koltuk 190 * 100": "2-<PERSON>ter Sofa 190 * 100", "KOL 1": "ARM 1", "KOL 2": "ARM 2", "KOL 3": "ARM 3", "KOL 4": "ARM 4", "KOL 5": "ARM 5", "KOL 6": "ARM 6", "KOL 7": "ARM 7", "BOŞ": "EMPTY", "CevizAhsap": "Walnut", "BeyazAhsap": "White", "EkruAhsap": "Ecru", "GriAhsap": "<PERSON>", "AntrasitAhsap": "Anthracite", "SariEskitmeAhsap": "Yellow Antiqued", "GriEskitmeAhsap": "Gray Antiqued", "CevizEskitmeAhsap": "Walnut Antiqued", "Bronz": "Bronze", "Gold": "Gold", "Nickel": "<PERSON><PERSON>", "woodenColors": "Wooden Colors", "legColors": "Leg Colors", "çektirme": "<PERSON><PERSON>", "tek parça": "Single Piece", "bombe": "Convex", "seatSubOption1": "Seat Option 1", "seatSubOption2": "Seat Option 2", "seatSubOption3": "Seat Option 3", "barcodeExplanation": "Barcode Description", "fabricCode": "Fabric Code", "fabricCodeDesc": "Fabric type and color number", "armrestCode": "Armrest Code", "armrestCodeDesc": "Armrest type and additional parts", "seatCode": "Seat Code", "seatCodeDesc": "Seat type and additional parts", "baseFrameCode": "Base Frame", "baseFrameCodeDesc": "Base frame and wood veneer number", "legColorCode": "Leg Color", "legColorCodeDesc": "Leg color index", "woodColorCode": "Wood Color", "woodColorCodeDesc": "Wood color index", "bergereNotImplemented": "Bergere option is not yet implemented", "düğmeKapiton": "Button Capitone", "boğumKapiton": "Joint Capitone", "kırışıklık": "Wrinkle", "fabric": "<PERSON><PERSON><PERSON>", "selectCatalog": "Select Catalog", "skeletonColor": "Skeleton Color", "selectSkeletonColor": "Select Skeleton Color", "price": "Price", "loading": "Loading...", "skeletonOptions": "Skeleton Options", "skeleton": "Skeleton", "woodColor": "Wood Color", "selectWoodColor": "Select Wood Color", "handleModelLoading": "3D Model Loading...", "enterFullscreen": "Enter fullscreen", "decorativeCushions": "Decorative Cushions", "fillMaterial": "Fill Material", "fabricCatalog": "<PERSON><PERSON><PERSON>", "quantity": "Quantity", "cushionSize": "Cushion Size", "designSaved": "Design successfully saved!", "designSaveFailed": "Failed to save design", "addedToCart": "Added to cart!", "addToCartFailed": "Failed to add to cart", "arm": "Arm", "armOptions": "Arm Options", "button": "<PERSON><PERSON>", "joint": "Joint", "frontFlap": "Front Flap", "skeletonAndSeat": "Skeleton and Seat", "selectType": "Select Type", "selectLegType": "Select Leg Type", "selectLegColor": "Select Color", "pull": "<PERSON><PERSON>", "singlePiece": "Single Piece", "convex": "Convex", "wrinkle": "Wrinkle", "designAndPriceDetails": "Design and Price Details", "customer": "Customer", "product": "Product", "backPillowLabel": "Back Pillow", "pillowLabel": "Pillow", "size": "Size", "color1": "Color 1", "color2": "Color 2", "color3": "Color 3", "color4": "Color 4", "color5": "Color 5", "color6": "Color 6", "color7": "Color 7", "color8": "Color 8", "color9": "Color 9", "fillMaterialLabel": "Fill Material:", "fabricCatalogLabel": "<PERSON><PERSON><PERSON>:", "colorLabel": "Color:", "quantityLabel": "Quantity:", "backPillowTitle": "Back Pillow", "decorativePillowTitle": "Decorative Pillow", "sizeInCm": "Size (cm):", "colorOption": "Color {{number}}", "sizeOption": "{{width}}x{{height}} cm", "frameType": "Lower Frame", "selectFrameType": "Select Frame", "frameType1": "Frame 1", "frameType2": "Frame 2", "frameType3": "Frame 3", "pleaseEnterCustomerName": "Please enter customer name and surname"}