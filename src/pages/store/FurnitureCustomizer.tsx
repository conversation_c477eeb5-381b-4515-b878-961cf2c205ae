import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { BsFullscreen } from 'react-icons/bs';
import BabylonScene from '@/components/store/BabylonScene';
import {
  backendService,
  apiService,
  PricingDataDto,
  PartPrice,
} from '@/services/api';
import { useCart } from '@/hooks/useCart';
import { toast } from 'react-toastify';

// Memoized BabylonScene to prevent re-renders when customer info changes
const MemoizedBabylonScene = memo(BabylonScene, (prevProps, nextProps) => {
  // Custom comparison function - only re-render if design actually changes
  return (
    JSON.stringify(prevProps.design) === JSON.stringify(nextProps.design) &&
    prevProps.furnitureType === nextProps.furnitureType &&
    prevProps.backCushion === nextProps.backCushion &&
    prevProps.getPartFilePath === nextProps.getPartFilePath &&
    JSON.stringify(prevProps.decorativeCushions) === JSON.stringify(nextProps.decorativeCushions)
  );
});

// BabylonScene compatible design type
interface BabylonDesign {
  furnitureType: 'armchair' | 'bergere';
  frameColor: string;
  upholstery: string;
  legType: string;
  cushionSize: string;
  cushionFabric: string;
  backCushion: string;
  decorativeCushions: {
    size: string;
    fabric: string;
    quantity: number;
  };
  addOns: string[];

  // Additional properties used in BabylonScene
  lowerFrame: string;
  sharedFabric: string;
  legs: string;
  legFabric: string;
  woodVeneer: string;
  woodVeneerFabric: string;
  armrest: {
    main: string;
    sub: string[];
  };
  cushion: {
    option: string;
  };
  // Seat options
  seat: {
    type: string;
    color: string;
    options: {
      cektirme: boolean;
      tekParca: boolean;
      bombe: boolean;
      kirisiklik: boolean;
      kulak: boolean;
    };
  };
  // Fabric options
  fabric: {
    type: string; // e.g., 'brown', 'grey', 'cartela1'
    color: string; // e.g., '1', '2', '3', etc.
  };
  // Skeleton options for bergere
  skeleton?: {
    type: string;
    color: string;
  };
}

interface CustomerInfo {
  firstName: string;
  lastName: string;
}

interface Design {
  furnitureType: string;
  fabric: {
    type: string;
    color: string;
  };
  frame: {
    type: string;
    color: string;
  };
  arm: {
    type: string;
    color: string;
    hasPapel: boolean;
    hasFrontFlap: boolean;
    hasKulak: boolean;
    hasDugme?: boolean;
    hasBoğumKapiton?: boolean;
  };
  seat: {
    type: string;
    color: string;
    options: {
      cektirme: boolean;
      tekParca: boolean;
      bombe: boolean;
      kirisiklik: boolean;
      kulak: boolean;
    };
  };
  leg: {
    type: string;
    color: string;
  };
  skeleton?: {
    type: string;
    color: string;
  };
}

interface Pillows {
  backPillow: {
    type: string;
    fabric: string;
    color: string;
    quantity: number;
  };
  pillow1: {
    cushion: string;
    fabric: string;
    color: string;
    quantity: number;
  };
  pillow2: {
    cushion: string;
    fabric: string;
    color: string;
    quantity: number;
  };
  pillow3: {
    cushion: string;
    fabric: string;
    color: string;
    quantity: number;
  };
}

interface PriceBreakdown {
  framePrice: number;
  fabricPrice: number;
  legPrice: number;
  cushionPrice: number;
  additionalOptionsPrice: number;
  skeletonPrice: number;
  totalPrice: number;
}

interface AllPartPrices {
  frames: Record<string, number>;
  fabrics: Record<string, number>;
  legs: Record<string, number>;
  seats: Record<string, number>;
  arms: Record<string, number>;
  armOptions: Record<string, number>;
  woodColors: Record<string, number>;
  pillows: Record<string, number>;
}

const FurnitureCustomizer: React.FC = () => {
  // Translation hook
  const { t } = useTranslation('furnitureCustomizer');

  // State and refs remain largely the same
  const navigate = useNavigate();
  const location = useLocation();
  const { fetchCart } = useCart();

  const [isModelLoading, setIsModelLoading] = useState<boolean>(true);

  // Rest of the state definitions...
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    firstName: '',
    lastName: '',
  });
  const [priceBreakdown, setPriceBreakdown] = useState<PriceBreakdown>({
    framePrice: 0,
    fabricPrice: 0,
    legPrice: 0,
    cushionPrice: 0,
    additionalOptionsPrice: 0,
    skeletonPrice: 0,
    totalPrice: 0,
  });
  const [isLoadingPrice, setIsLoadingPrice] = useState<boolean>(false);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const [barcodeText, setBarcodeText] = useState<string>(
    'K1(1)_1-1-1(1)-1-1',
  );

  // Add state to track if saved design has been loaded
  const [savedDesignLoaded, setSavedDesignLoaded] = useState<boolean>(false);

  // Reset savedDesignLoaded when location.state changes or component mounts without saved design
  useEffect(() => {
    if (!location.state?.savedDesign) {
      setSavedDesignLoaded(false);
    }
  }, [location.state]);

  // State for BabylonScene configuration
  const [babylonDesign, setBabylonDesign] = useState<BabylonDesign>({
    furnitureType: 'armchair',
    frameColor: 'CevizAhsap',
    upholstery: 'brown',
    legType: '1 Ahşap',
    cushionSize: '50*50',
    cushionFabric: 'brown',
    backCushion: 'Silikon Elyaf',
    decorativeCushions: {
      size: '50*50',
      fabric: 'brown',
      quantity: 1,
    },
    addOns: [],

    // Default values for BabylonScene
    lowerFrame: '1',
    sharedFabric: 'brown',
    legs: '1',
    legFabric: 'CevizAhsap',
    woodVeneer: '1',
    woodVeneerFabric: 'CevizAhsap',
    armrest: {
      main: '1',
      sub: [],
    },
    cushion: {
      option: '1',
    },
    seat: {
      type: '1',
      color: 'brown',
      options: {
        cektirme: false,
        tekParca: false,
        bombe: false,
        kirisiklik: false,
        kulak: false,
      },
    },
    // Initialize fabric options with cartela1 as default
    fabric: {
      type: 'cartela1',
      color: '1',
    },
  });
  const [design, setDesign] = useState<Design>({
    furnitureType: 'armchair',
    fabric: {
      type: 'cartela1',
      color: '1',
    },
    frame: {
      type: '1',
      color: 'CevizAhsap',
    },
    arm: {
      type: 'KOL 1',
      color: 'CevizAhsap',
      hasPapel: false,
      hasFrontFlap: false,
      hasKulak: false,
      hasDugme: false,
      hasBoğumKapiton: false,
    },
    seat: {
      type: '1',
      color: 'brown',
      options: {
        cektirme: false,
        tekParca: false,
        bombe: false,
        kirisiklik: false,
        kulak: false,
      },
    },
    leg: {
      type: '1 Ahşap',
      color: 'CevizAhsap',
    },
    skeleton: {
      type: '1',
      color: 'CevizAhsap',
    },
  });

  // State for selected skeleton option in bergere
  const [selectedSkeleton, setSelectedSkeleton] = useState<string>('1');

  // Separate state for pillows to prevent unnecessary 3D re-renders
  const [pillows, setPillows] = useState<Pillows>({
    backPillow: {
      type: 'Yok',
      fabric: 'Kartela 1',
      color: '1',
      quantity: 1
    },
    pillow1: {
      cushion: 'Yok',
      fabric: 'Kartela 1',
      color: '1',
      quantity: 1
    },
    pillow2: {
      cushion: 'Yok',
      fabric: 'Kartela 1',
      color: '1',
      quantity: 1
    },
    pillow3: {
      cushion: 'Yok',
      fabric: 'Kartela 1',
      color: '1',
      quantity: 1
    },
  });

  // FIXED: Improved getPartFilePath function for correct file path generation
  const getPartFilePath = useCallback((type: string, subType: string, name: string): string => {
    if (!name || name === '') return '';

    console.log(`Getting path for ${subType}: ${name}`);

    // Map to the correct file paths in your assets structure
    switch (subType) {
      case 'lowerFrame':
        return `/assets/${type}/objects/AltKasa/${name}`;

      case 'legs':
        // The leg files have names like "1 Ahşap.glb", "3 Metal.glb"
        // If name is just a number (e.g., "1"), append the appropriate type
        if (/^\d+$/.test(name)) {
          const legFullName = `${name} Ahşap`; // Default to Ahşap (wooden) if only number is provided
          return `/assets/${type}/objects/Ayaklar/${legFullName}`;
        }
        // Otherwise use the full name as provided
        return `/assets/${type}/objects/Ayaklar/${name}`;

      case 'woodVeneer':
        return `/assets/${type}/objects/AhsapKasa/${name}`;

      case 'armrest':
        // Handle different arm naming conventions based on file structure
        // Use file selection based on if this is the BOŞ or Kulak (4) option

        // First check if this is a sub-option case like "KOL 1:3-5" (for Papel, Kulak, etc.)
        if (name.includes(':')) {
          const [armBase, option] = name.split(':');

          // Handle armrest sub-options
          if (/^\d+(-\d+)*$/.test(option)) {
            // This is a numeric option or combination (3, 4, 5, 3-5, etc.)
            console.log(`Loading armrest sub-option: ${armBase}/${option}`);
            return `/assets/${type}/objects/Kol/${armBase}/${option}`;
          }

          // Check if we need to load the Kulak model (4) or the default BOŞ model
          const modelToLoad = option === '4' ? '4' : 'BOŞ';
          console.log(`Loading main armrest: ${armBase}/${modelToLoad}`);
          return `/assets/${type}/objects/Kol/${armBase}/${modelToLoad}`;
        }

        // Handle basic numbered armrests without options
        if (/^\d+$/.test(name)) {
          // If only a number is provided, use KOL format
          return `/assets/${type}/objects/Kol/KOL ${name}/BOŞ`;
        } else if (name.startsWith('KOL')) {
          // Extract the number part from "KOL X"
          const armNumber = name.split(' ')[1];
          console.log(`Loading armrest: KOL ${armNumber}/BOŞ`);
          return `/assets/${type}/objects/Kol/KOL ${armNumber}/BOŞ`;
        } else {
          // Use as is (fallback)
          console.log(`Loading armrest fallback: ${name}/BOŞ`);
          return `/assets/${type}/objects/Kol/${name}/BOŞ`;
        }

      case 'armrestSub':
        // Special sub components for armrests
        if (name === 'papel') return `/assets/${type}/objects/Kol/papel`;
        if (name === 'onKlapa') return `/assets/${type}/objects/Kol/onKlapa`;
        return '';

      case 'armrestSubOptions':
        // Alias to support BabylonScene use of 'armrestSubOptions'
        // Expected format: "KOL 1:3" where left is arm directory, right is code
        if (name.includes(':')) {
          const [dir, code] = name.split(':');
          return `/assets/${type}/objects/Kol/${dir}/${code}`;
        }
        return '';

      case 'cushion':
        // Based on the file structure, cushions are in Minder/Kulaksız/[type]
        // The default file to load should be BOŞ.glb
        if (/^\d+$/.test(name)) {
          // If just a number is provided, use the BOŞ file for that type
          console.log(`Loading cushion type: ${name}/BOŞ`);
          return `/assets/${type}/objects/Minder/Kulaksız/${name}/BOŞ`;
        } else {
          // Otherwise use the provided name directly
          console.log(`Loading cushion: ${name}`);
          return `/assets/${type}/objects/Minder/Kulaksız/${name}`;
        }

      case 'seat':
        // For bergere, seat is included in skeleton.glb file, so we don't load it separately
        if (type === 'bergere') {
          console.log(`🔍 Bergere seat is included in skeleton.glb, not loading separately`);
          return ''; // Return empty path to skip loading
        }
        // For seat models, determine folder based on armrest Kulak selection
        if (name.startsWith('seatFile:')) {
          // Format: "seatFile:seatType:optionList" (e.g., "seatFile:1:1-3-4" or "seatFile:2:")
          const parts = name.split(':');
          const seatType = parts[1]; // 1, 2, or 3
          const optionString = parts[2] || 'BOŞ'; // Use BOŞ as default if no options

          // Use Kulaklı if armrest Kulak is selected, otherwise Kulaksız
          const seatDir = design.arm.hasKulak ? 'Kulaklı' : 'Kulaksız';
          console.log(`Loading seat file: ${seatDir}/${seatType}/${optionString}`);
          return `/assets/${type}/objects/Minder/${seatDir}/${seatType}/${optionString}`;
        }

        // If just a seat type number is provided (1, 2, 3)
        if (/^\d+$/.test(name)) {
          // Default to the empty model for the given seat type
          // Use Kulaklı if armrest Kulak is selected, otherwise Kulaksız
          const seatDir = design.arm.hasKulak ? 'Kulaklı' : 'Kulaksız';
          console.log(`Loading basic seat type: ${seatDir}/${name}/BOŞ`);
          return `/assets/${type}/objects/Minder/${seatDir}/${name}/BOŞ`;
        }

        // Fallback case
        console.warn(`Unhandled seat path request: ${name}`);
        return `/assets/${type}/objects/Minder/Kulaksız/1/BOŞ`; // Fallback to default seat

      case 'skeleton':
        // For bergere skeleton - files are in subdirectories (skeleton/1/skeleton.glb, skeleton/2/skeleton.glb, etc.)
        if (type === 'bergere') {
          // Extract just the number if it's in format like "seatFile:1:BOŞ"
          let skeletonNumber = name;
          if (name.includes(':')) {
            const parts = name.split(':');
            skeletonNumber = parts[1] || '1';
          }
          const skeletonPath = `/assets/${type}/skeleton/${skeletonNumber}/skeleton`;
          console.log(`🔍 Skeleton path generated: ${skeletonPath} for skeleton number: ${skeletonNumber}`);
          return skeletonPath;
        }
        return `/assets/${type}/objects/skeleton/${name}`;

      case 'fabric':
        // For bergere fabric
        if (type === 'bergere') {
          const fabricPath = `/assets/${type}/objects/fabric/${name}`;
          console.log(`🔍 Fabric path generated: ${fabricPath} for fabric: ${name}`);
          return fabricPath;
        }
        return `/assets/${type}/objects/fabric/${name}`;

      default:
        return '';
    }
  }, [design.arm.hasKulak]); // Include dependency for seat folder selection

  // Update BabylonDesign when regular design changes
  const updateBabylonDesign = useCallback(() => {
    // Build armrest sub-option codes - separate base types from additional options
    const subOptions: string[] = [];
    const armDir = design.arm.type; // Use the selected KOL type (KOL 1, KOL 2, etc.)

    // Handle base types (1 and 2) - mutually exclusive
    const baseOptions: string[] = [];
    if (design.arm.hasDugme) baseOptions.push('1'); // 1 = Düğme Kapiton
    if (design.arm.hasBoğumKapiton) baseOptions.push('2'); // 2 = Boğum Kapiton

    // Handle additional options (3, 4, 5) - can be combined
    const additionalOptions: string[] = [];
    if (design.arm.hasPapel) additionalOptions.push('3'); // 3 = Papel
    if (design.arm.hasKulak) additionalOptions.push('4'); // 4 = Kulak
    if (design.arm.hasFrontFlap) additionalOptions.push('5'); // 5 = Ön Klapa

    // Create separate file paths for base type and additional options
    if (baseOptions.length > 0) {
      // Base type file (1 or 2), potentially with Kulak (4)
      if (design.arm.hasKulak) {
        // Base type with Kulak: "1-4" or "2-4"
        subOptions.push(`${armDir}:${baseOptions[0]}-4`);
      } else {
        // Base type alone: "1" or "2"
        subOptions.push(`${armDir}:${baseOptions[0]}`);
      }
    }

    // Handle additional options (3, 5) - always include Kulak (4) if selected
    if (additionalOptions.length > 0) {
      // If we have Papel (3) or Ön Klapa (5), and Kulak (4) is selected
      const hasNonKulakOptions = additionalOptions.some(opt => opt !== '4');
      if (hasNonKulakOptions && design.arm.hasKulak) {
        // Include all additional options including Kulak: "3-4-5"
        const allAdditional = additionalOptions.sort((a, b) => parseInt(a) - parseInt(b));
        subOptions.push(`${armDir}:${allAdditional.join('-')}`);
      } else if (hasNonKulakOptions && !design.arm.hasKulak) {
        // Only non-Kulak additional options: "3-5"
        const nonKulakOptions = additionalOptions.filter(opt => opt !== '4');
        subOptions.push(`${armDir}:${nonKulakOptions.join('-')}`);
      } else if (!hasNonKulakOptions && design.arm.hasKulak && !baseOptions.length) {
        // Only Kulak without base type: "4"
        subOptions.push(`${armDir}:4`);
      }
    }

    // Build seat file parameter including selected seat sub-options
    const seatOptionCodes: string[] = [];
    if (design.seat.options.cektirme) seatOptionCodes.push('1');
    if (design.seat.options.tekParca) seatOptionCodes.push('3');
    if (design.seat.options.bombe) seatOptionCodes.push('4');
    const seatOptionString = seatOptionCodes.length > 0 ? seatOptionCodes.join('-') : 'BOŞ';
    const seatFileParam = `seatFile:${design.seat.type}:${seatOptionString}`;

    console.log('Updating Babylon Design:', design);

    // Set the corresponding options for BabylonScene
    setBabylonDesign({
      // Basic properties for BabylonScene
      furnitureType: design.furnitureType as 'armchair' | 'bergere',
      frameColor: design.frame.color,
      upholstery: design.fabric.type,
      legType: design.leg.type,
      cushionSize: design.seat.type,
      cushionFabric: design.fabric.type,
      backCushion: pillows.backPillow.type, // Use the actual back pillow type
      decorativeCushions: {
        size: '50*50', // Static value as pillows don't affect 3D model
        fabric: design.fabric.type,
        quantity: 1,
      },
      addOns: [],

      // BabylonScene specific mapping - these properties match the paths in BabylonScene.tsx
      // and are used to load the correct 3D models
      lowerFrame: design.frame.type, // Use selected frame type for AltKasa
      sharedFabric: design.fabric.type, // Shared fabric for most parts
      legs: design.leg.type, // For Ayaklar (Legs)
      legFabric: design.leg.color, // Leg color/material
      woodVeneer: design.frame.type, // Use selected frame type for AhsapKasa
      woodVeneerFabric: design.frame.color, // Wood veneer color/material
      armrest: {
        // Main armrest logic: Load BOŞ/4 only when NO base types (Düğme/Boğum) are selected
        main: (() => {
          const hasBaseType = design.arm.hasDugme || design.arm.hasBoğumKapiton;

          if (hasBaseType) {
            // If base types are selected, don't load main (sub-options will handle it)
            return '';
          } else {
            // No base types selected
            if (design.arm.hasKulak) {
              // Kulak selected but no base types: load 4.glb
              return `${armDir}:4`;
            } else {
              // No Kulak and no base types: load BOŞ.glb
              return `${armDir}:BOŞ`;
            }
          }
        })(),
        sub: subOptions, // Separate armrest component files (1, 2, 1-4, 2-4, 3-4-5, etc.)
      },
      cushion: {
        option: design.seat.type, // For seat cushion type (Kulaksız or Kulaklı)
      },
      // Add the seat property to match the BabylonDesign interface
      seat: {
        type: seatFileParam,
        color: design.seat.color,
        options: design.seat.options,
      },
      // Add the fabric property
      fabric: {
        type: design.fabric.type,
        color: design.fabric.color,
      },
      // Add skeleton property for bergere
      skeleton: design.skeleton,
    });
  }, [
    design.furnitureType,
    design.frame,
    design.fabric,
    design.leg,
    design.arm,
    design.seat,
    design.skeleton,
    pillows.backPillow.type
  ]); // Only depend on fields that affect the 3D model

  // Event handlers remain mostly the same
  const handleFurnitureTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newFurnitureType = e.target.value as 'armchair' | 'bergere';
    setDesign({ ...design, furnitureType: newFurnitureType });
    // Update babylon design immediately with the new furniture type
    setBabylonDesign(prev => ({ ...prev, furnitureType: newFurnitureType }));
  };

  // Other handlers...
  const handleFabricTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setDesign({ ...design, fabric: { ...design.fabric, type: e.target.value } });
  };

  const handleFabricColorChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setDesign({ ...design, fabric: { ...design.fabric, color: e.target.value } });
  };

  const handleArmTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setDesign({ ...design, arm: { ...design.arm, type: e.target.value } });
  };

  const handleArmOptionChange = (option: keyof Design['arm'], checked: boolean) => {
    let newArmState: any = { ...design.arm };

    // Handle mutually exclusive options 1 and 2
    if (option === 'hasDugme' && checked) {
      // Clear option 2 when selecting option 1
      newArmState = { ...newArmState, hasBoğumKapiton: false, [option]: checked };
    } else if (option === 'hasBoğumKapiton' && checked) {
      // Clear option 1 when selecting option 2
      newArmState = { ...newArmState, hasDugme: false, [option]: checked };
    } else {
      newArmState[option] = checked;
    }

    setDesign({ ...design, arm: newArmState });
  };

  const handleSeatTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setDesign({ ...design, seat: { ...design.seat, type: e.target.value } });
  };

  const handleSeatOptionChange = (option: string, value: string | boolean) => {
    if (option === 'options') {
      // Handle checkbox options
      const optionName = value as string;
      const isChecked = !design.seat.options[optionName as keyof typeof design.seat.options];

      setDesign({
        ...design,
        seat: {
          ...design.seat,
          options: {
            ...design.seat.options,
            [optionName]: isChecked,
          },
        },
      });
    } else {
      // Handle dropdown options
      setDesign({ ...design, seat: { ...design.seat, [option]: value } });
    }
  };

  const handleFrameTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setDesign({ ...design, frame: { ...design.frame, type: e.target.value } });
  };

  const handleFrameColorChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setDesign({ ...design, frame: { ...design.frame, color: e.target.value } });
  };

  const handleLegTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newLegType = e.target.value;
    const isMetalLeg = newLegType.includes('Metal');
    const isWoodenLeg = newLegType.includes('Ahşap');

    // Reset color to appropriate default when switching between wooden and metal
    let newColor = design.leg.color;
    if (isWoodenLeg && ['Bronz', 'Gold', 'Nikel'].includes(design.leg.color)) {
      // If switching to wooden leg but current color is metal, reset to default wooden color
      newColor = 'BeyazAhsap'; // Default to Beyaz instead of Ceviz
    } else if (isMetalLeg && design.leg.color === 'CevizAhsap') {
      // If switching to metal leg and current color is Ceviz, reset to default metal color
      newColor = 'Bronz';
    } else if (isMetalLeg && design.leg.color.includes('Ahsap') && design.leg.color !== 'CevizAhsap') {
      // Keep other wooden colors when switching to metal, only remove Ceviz
      // No change needed for other wooden colors
    }

    setDesign({
      ...design,
      leg: {
        ...design.leg,
        type: newLegType,
        color: newColor
      }
    });
  };

  const handleLegColorChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setDesign({ ...design, leg: { ...design.leg, color: e.target.value } });
  };

  const handlePillowChange = (pillow: keyof Pillows, field: string, value: string | number) => {
    const newPillows = {
      ...pillows,
      [pillow]: {
        ...pillows[pillow],
        [field]: value
      }
    };

    setPillows(newPillows);

    // Recalculate price when pillow configuration changes
    setTimeout(() => {
      if (Object.keys(pricingData).length > 0) {
        calculateTotalPrice(pricingData, design, newPillows);
      }
    }, 100); // Small delay to ensure state update
  };

  const handleCustomerInfoChange = (field: keyof CustomerInfo, value: string) => {
    setCustomerInfo({ ...customerInfo, [field]: value });
    // Customer info doesn't affect the 3D model, so no need to update Babylon design
  };

  // Add error handler for 3D rendering

  // Store all part prices
  const [pricingData, setPricingData] = useState<any>({});
  const [allPartPrices, setAllPartPrices] = useState<AllPartPrices>({
    frames: {},
    fabrics: {},
    legs: {},
    seats: {},
    arms: {},
    armOptions: {},
    woodColors: {},
    pillows: {},
  });

  // Calculate total price based on selected parts
  // const calculateTotalPrice = useCallback((prices: AllPartPrices = allPartPrices) => {
  //   try {
  //     // Start with base prices
  //     let framePrice = prices.frames[design.frame.type] || 0;
  //     let fabricPrice = prices.fabrics[design.fabric.type] || 0;

  //     // For legs, try both formats (with and without the type suffix)
  //     let legPrice = prices.legs[design.leg.type] ||
  //       prices.legs[design.leg.type.split(' ')[0]] || 0;

  //     let seatPrice = prices.seats[design.seat.type] || 0;

  //     // For arms, try both formats
  //     let armPrice = prices.arms[design.arm.type] ||
  //       prices.arms[design.arm.type.split(' ')[1]] || 0;

  //     let additionalOptionsPrice = 0;
  //     let cushionPrice = 0;

  //     // Add woodColor price
  //     framePrice += prices.woodColors[design.frame.color] || 0;

  //     // Add arm option prices if selected
  //     if (design.arm.hasPapel) {
  //       additionalOptionsPrice += prices.armOptions['hasPapel'] || 0;
  //     }
  //     if (design.arm.hasKulak) {
  //       additionalOptionsPrice += prices.armOptions['hasKulak'] || 0;
  //     }
  //     if (design.arm.hasFrontFlap) {
  //       additionalOptionsPrice += prices.armOptions['hasFrontFlap'] || 0;
  //     }

  //     // Add pillow prices, checking if "Yok" (None) is selected
  //     if (design.pillows.backPillow && design.pillows.backPillow !== 'Yok') {
  //       cushionPrice += prices.pillows[design.pillows.backPillow] || 0;
  //     }
  //     if (design.pillows.pillow1 && design.pillows.pillow1 !== 'Yok') {
  //       cushionPrice += prices.pillows[design.pillows.pillow1] || 0;
  //     }
  //     if (design.pillows.pillow2 && design.pillows.pillow2 !== 'Yok') {
  //       cushionPrice += prices.pillows[design.pillows.pillow2] || 0;
  //     }
  //     if (design.pillows.pillow3 && design.pillows.pillow3 !== 'Yok') {
  //       cushionPrice += prices.pillows[design.pillows.pillow3] || 0;
  //     }

  //     // Calculate total
  //     const totalPrice = framePrice + fabricPrice + legPrice + seatPrice + armPrice + additionalOptionsPrice + cushionPrice;

  //     // Update price breakdown
  //     setPriceBreakdown({
  //       framePrice,
  //       fabricPrice,
  //       legPrice,
  //       cushionPrice,
  //       additionalOptionsPrice,
  //       totalPrice,
  //     });
  //   } catch (error) {
  //     console.error("Error calculating price:", error);
  //     // Set fallback price
  //     setPriceBreakdown((prev) => ({
  //       ...prev,
  //       totalPrice: 40000, // Simple fallback
  //     }));
  //   }
  // }, [design, allPartPrices]);

  // Transform backend pricing DTO or array data into lookup object
  const processPriceData = useCallback((priceData: PricingDataDto | any) => {
    // Convert to lookup object for faster price calculations
    const priceLookup: AllPartPrices = {
      frames: {},
      fabrics: {},
      legs: {},
      seats: {},
      arms: {},
      armOptions: {},
      woodColors: {},
      pillows: {},
    };

    console.log('Processing price data:', priceData);

    // Handle the new API response structure
    if (priceData) {
      // Frames / lower frame base
      if (priceData.lowerFrame) {
        Object.entries(priceData.lowerFrame).forEach(([k, v]) => (priceLookup.frames[k] = v as number));
      }

      // Seat options
      if (priceData.seatOptions) {
        Object.entries(priceData.seatOptions).forEach(([k, obj]) => {
          // Map seat options by number (1, 2, 3)
          const seatNum = k.replace('OturumSeçenek ', '');
          priceLookup.seats[seatNum] = (obj as any).price ?? 0;
        });
      }

      // Armrest base prices (for KOL 1, KOL 2, etc.)
      if (priceData.armrestBase) {
        Object.entries(priceData.armrestBase).forEach(([k, obj]) => {
          // Map "Base 1" to "KOL 1"
          const kolNum = k.replace('Base ', 'KOL ');
          priceLookup.arms[kolNum] = (obj as any).price ?? 0;
        });
      }

      // Arm extensions (papel, klapa) - now using legacy fields
      if (priceData.armrestPapel) {
        // Map armrestPapel - key '3' is for Papel
        if (priceData.armrestPapel['3']) {
          priceLookup.armOptions['hasPapel'] = priceData.armrestPapel['3'].price ?? 0;
        }
      }
      if (priceData.armrestKlapa) {
        // Map armrestKlapa - key '5' is for Klapa
        if (priceData.armrestKlapa['5']) {
          priceLookup.armOptions['hasFrontFlap'] = priceData.armrestKlapa['5'].price ?? 0;
        }
      }
      // For hasKulak, check armrestBase['4'] since Kulak is option 4
      if (priceData.armrestBase && priceData.armrestBase['4']) {
        priceLookup.armOptions['hasKulak'] = priceData.armrestBase['4'].price ?? 0;
      }

      // Legs
      if (priceData.legOptions) {
        Object.entries(priceData.legOptions).forEach(([k, v]) => {
          priceLookup.legs[k] = v as number;
          // Also map with full names
          priceLookup.legs[`${k} Ahşap`] = v as number;
          priceLookup.legs[`${k} Metal`] = v as number;
        });
      }

      // Wood colors (for legs and frames)
      if (priceData.legColors) {
        Object.entries(priceData.legColors).forEach(([k, v]) => {
          // Map color names to match UI values
          const mappedColorNames: Record<string, string> = {
            'Ceviz': 'CevizAhsap',
            'Beyaz': 'BeyazAhsap',
            'Ekru': 'EkruAhsap',
            'Gri': 'GriAhsap',
            'Siyah': 'AntrasitAhsap', // Map Siyah to AntrasitAhsap
            'Sarı Eskitme': 'SariEskitmeAhsap',
            'Siyah Eskitme': 'GriEskitmeAhsap', // Map Siyah Eskitme to GriEskitmeAhsap
            'Ceviz Eskitme': 'CevizEskitmeAhsap'
          };

          const mappedKey = mappedColorNames[k] || `${k}Ahsap`;
          priceLookup.woodColors[mappedKey] = v as number;
        });
      }
      if (priceData.lowerFrameColors) {
        Object.entries(priceData.lowerFrameColors).forEach(([k, v]) => {
          // Map color names to match UI values
          const mappedColorNames: Record<string, string> = {
            'Ceviz': 'CevizAhsap',
            'Beyaz': 'BeyazAhsap',
            'Ekru': 'EkruAhsap',
            'Gri': 'GriAhsap',
            'Siyah': 'AntrasitAhsap', // Map Siyah to AntrasitAhsap
            'Sarı Eskitme': 'SariEskitmeAhsap',
            'Siyah Eskitme': 'GriEskitmeAhsap', // Map Siyah Eskitme to GriEskitmeAhsap
            'Ceviz Eskitme': 'CevizEskitmeAhsap'
          };

          const mappedKey = mappedColorNames[k] || `${k}Ahsap`;
          priceLookup.woodColors[mappedKey] = v as number;
        });
      }

      // Fabrics
      if (priceData.fabrics) {
        Object.entries(priceData.fabrics).forEach(([fabricType, colors]) => {
          Object.entries(colors as any).forEach(([, price]) => {
            priceLookup.fabrics[fabricType] = Math.max(priceLookup.fabrics[fabricType] || 0, price as number);
          });
        });
      }

      // Pillows
      if (priceData.backPillows) {
        Object.entries(priceData.backPillows).forEach(([k, obj]) => {
          priceLookup.pillows[k] = (obj as any).price ?? 0;
        });
      }
      if (priceData.cushions) {
        Object.entries(priceData.cushions).forEach(([k, obj]) => {
          priceLookup.pillows[k] = (obj as any).price ?? 0;
        });
      }

      // Store general outcome and profit rate for later use
      if (priceData.generalOutcomeArmchair) {
        (priceLookup as any).generalOutcomeArmchair = priceData.generalOutcomeArmchair;
      }
      if (priceData.profitRate) {
        (priceLookup as any).profitRate = priceData.profitRate;
      }
      if (priceData.fixedCost) {
        (priceLookup as any).fixedCost = priceData.fixedCost;
      }
    } else {
      // Fallback: original array-based structure
      if (priceData.frames && Array.isArray(priceData.frames)) {
        priceData.frames.forEach((item: PartPrice) => (priceLookup.frames[item.id] = item.price));
      }
      if (priceData.fabrics && Array.isArray(priceData.fabrics)) {
        priceData.fabrics.forEach((item: PartPrice) => (priceLookup.fabrics[item.id] = item.price));
      }
      if (priceData.legs && Array.isArray(priceData.legs)) {
        priceData.legs.forEach((item: PartPrice) => {
          const legId = item.id.split(' ')[0];
          priceLookup.legs[legId] = item.price;
          priceLookup.legs[item.id] = item.price;
        });
      }
      if (priceData.seats && Array.isArray(priceData.seats)) {
        priceData.seats.forEach((item: PartPrice) => (priceLookup.seats[item.id] = item.price));
      }
      if (priceData.arms && Array.isArray(priceData.arms)) {
        priceData.arms.forEach((item: PartPrice) => (priceLookup.arms[item.id] = item.price));
      }
      if (priceData.armOptions && Array.isArray(priceData.armOptions)) {
        priceData.armOptions.forEach((item: PartPrice) => (priceLookup.armOptions[item.id] = item.price));
      }
      if (priceData.woodColors && Array.isArray(priceData.woodColors)) {
        priceData.woodColors.forEach((item: PartPrice) => (priceLookup.woodColors[item.id] = item.price));
      }
      if (priceData.pillows && Array.isArray(priceData.pillows)) {
        priceData.pillows.forEach((item: PartPrice) => (priceLookup.pillows[item.id] = item.price));
      }
    }
    console.log('Processed price lookup:', priceLookup);
    setAllPartPrices(priceLookup);
    setPricingData(priceData); // Store the raw pricing data
    // Calculate initial prices with raw pricing data
    calculateTotalPrice(priceData);
  }, []);
  // Fetch all prices on component mount
  const fetchAllPartPrices = useCallback(async () => {
    try {
      setIsLoadingPrice(true);

      try {
        // Try to call actual API first
        const pricingDto = await backendService.getPricingData();
        console.log('Received pricing data from API:', pricingDto);
        processPriceData(pricingDto);
      } catch (apiError) {
        console.warn('Pricing API call failed:', apiError);
        // Use mock data if API call fails
        processPriceData({});
      }
    } catch (error) {
      console.error('Error in price handling:', error);

    } finally {
      setIsLoadingPrice(false);
    }
  }, [processPriceData]);

  // This will be moved after calculateTotalPrice definition

  // Calculate total price based on selected parts using new backend structure
  const calculateTotalPrice = useCallback((pricingData: any = {}, currentDesign = design, currentPillows = pillows) => {
    try {
      console.log('calculateTotalPrice called with pricing data:', pricingData);
      console.log('Current design:', currentDesign);
      console.log('Current pillows:', currentPillows);

      let totalPrice = 0;

      // 1. Start with general outcome (fixed cost) based on furniture type
      if (currentDesign.furnitureType === 'armchair') {
        totalPrice += pricingData.generalOutcomeArmchair || 0;
      } else if (currentDesign.furnitureType === 'bergere') {
        totalPrice += pricingData.generalOutcomeBergere || 0;
      }

      // 2. Get fabric price per meter based on selected kartela and color
      let fabricPricePerMeter = 0;
      const fabricKartela = `Kartela ${currentDesign.fabric.type.replace('cartela', '')}`;
      if (pricingData.fabrics && pricingData.fabrics[fabricKartela]) {
        fabricPricePerMeter = pricingData.fabrics[fabricKartela][currentDesign.fabric.color] || 0;
      }

      // 3-6. Calculate furniture-specific components based on type
      if (currentDesign.furnitureType === 'armchair') {
        // ARMCHAIR-SPECIFIC CALCULATIONS

        // 3. Calculate seat price and fabric usage (price + fabric metraj * fabric price)
        const seatKey = `OturumSeçenek ${currentDesign.seat.type}`;
        if (pricingData.seatOptions && pricingData.seatOptions[seatKey]) {
          const seatData = pricingData.seatOptions[seatKey];
          totalPrice += seatData.price || 0;

          // Seat fabric calculation: seat fabric metraj * fabric price per meter
          const seatFabricCost = (seatData.fabricAmount || 0) * fabricPricePerMeter;
          totalPrice += seatFabricCost;
        }

        // 4. Calculate armrest price and fabric usage based on selected options
        // Check for base armrest type (KOL 1, KOL 2, etc.)
        const armNumber = currentDesign.arm.type.replace('KOL ', '');

        // Add base armrest price (for KOL 1, KOL 2, etc.)
        if (pricingData.armrestBase && pricingData.armrestBase[armNumber]) {
          const armBaseData = pricingData.armrestBase[armNumber];
          totalPrice += armBaseData.price || 0;
          const armBaseFabricCost = (armBaseData.fabricAmount || 0) * fabricPricePerMeter;
          totalPrice += armBaseFabricCost;
        }

        // Check for individual armrest options
        if (currentDesign.arm.hasKulak && pricingData.armrestBase && pricingData.armrestBase['4']) {
          const kulakData = pricingData.armrestBase['4'];
          totalPrice += kulakData.price || 0;
          const kulakFabricCost = (kulakData.fabricAmount || 0) * fabricPricePerMeter;
          totalPrice += kulakFabricCost;
        }

        if (currentDesign.arm.hasPapel && pricingData.armrestPapel && pricingData.armrestPapel['3']) {
          const papelData = pricingData.armrestPapel['3'];
          totalPrice += papelData.price || 0;
          const papelFabricCost = (papelData.fabricAmount || 0) * fabricPricePerMeter;
          totalPrice += papelFabricCost;
        }

        if (currentDesign.arm.hasFrontFlap && pricingData.armrestKlapa && pricingData.armrestKlapa['5']) {
          const klapaData = pricingData.armrestKlapa['5'];
          totalPrice += klapaData.price || 0;
          const klapaFabricCost = (klapaData.fabricAmount || 0) * fabricPricePerMeter;
          totalPrice += klapaFabricCost;
        }

        // 5. Add lower frame fixed price + color multiplier
        const lowerFrameBasePrice = pricingData.lowerFrameFixedPrice || 0;
        let lowerFrameColorMultiplier = 1;
        if (pricingData.lowerFrame && pricingData.lowerFrame[currentDesign.frame.color]) {
          lowerFrameColorMultiplier = pricingData.lowerFrame[currentDesign.frame.color];
        }
        totalPrice += lowerFrameBasePrice * lowerFrameColorMultiplier;

        // 6. Add leg price based on type and color (each leg has its own price + color price)
        const legNumber = currentDesign.leg.type.split(' ')[0]; // Extract "1" from "1 Ahşap"
        if (pricingData.legOptions && pricingData.legOptions[legNumber]) {
          const legColorPrice = pricingData.legOptions[legNumber][currentDesign.leg.color] || 0;
          totalPrice += legColorPrice;
        }

        console.log('Applied armchair-specific pricing calculations');
      } else if (currentDesign.furnitureType === 'bergere') {
        // BERGERE-SPECIFIC CALCULATIONS

        // For bergere, add bergere-specific costs (price + metraj * fabric price)
        if (pricingData.bergereOptions) {
          // Use skeleton type if available, otherwise default to 'Bergere 1'
          // The skeleton.type should already contain "Bergere 1", "Bergere 2", etc.
          const bergereType = currentDesign.skeleton?.type || 'Bergere 1';
          if (pricingData.bergereOptions[bergereType]) {
            const bergereData = pricingData.bergereOptions[bergereType];
            totalPrice += bergereData.price || 0;
            // Bergere fabric calculation: bergere metraj * fabric price per meter
            const bergereFabricCost = (bergereData.fabricAmount || 0) * fabricPricePerMeter;
            totalPrice += bergereFabricCost;

            console.log(`Applied bergere-specific pricing: ${bergereType}, base price: ${bergereData.price}, fabric cost: ${bergereFabricCost}`);
          } else {
            console.warn(`Bergere type "${bergereType}" not found in pricing data. Available types:`, Object.keys(pricingData.bergereOptions));
          }
        }
      }

      // 7. Add back pillow price if selected (with proper quantity calculation)
      let totalPillowCost = 0;
      if (currentPillows.backPillow.type && currentPillows.backPillow.type !== 'Yok' && currentPillows.backPillow.quantity > 0) {
        if (pricingData.backPillows && pricingData.backPillows[currentPillows.backPillow.type]) {
          const backPillowData = pricingData.backPillows[currentPillows.backPillow.type];

          // Calculate back pillow price per piece
          const backPillowUnitPrice = backPillowData.price || 0;

          // Get fabric price for back pillow based on selected kartela
          let backPillowFabricPricePerMeter = 0;
          if (pricingData.fabrics && pricingData.fabrics[currentPillows.backPillow.fabric]) {
            backPillowFabricPricePerMeter = pricingData.fabrics[currentPillows.backPillow.fabric][currentPillows.backPillow.color] || 0;
          }

          // Calculate fabric cost per unit
          const backPillowFabricCostPerUnit = (backPillowData.fabricAmount || 0) * backPillowFabricPricePerMeter;

          // Total cost per back pillow unit
          const backPillowTotalPerUnit = backPillowUnitPrice + backPillowFabricCostPerUnit;

          // Multiply by quantity
          const backPillowTotalCost = backPillowTotalPerUnit * currentPillows.backPillow.quantity;
          totalPrice += backPillowTotalCost;
          totalPillowCost += backPillowTotalCost;

          console.log(`Back Pillow: Unit price ${backPillowUnitPrice}, Fabric per unit ${backPillowFabricCostPerUnit}, Quantity ${currentPillows.backPillow.quantity}, Total ${backPillowTotalCost}`);
        }
      }

      // 8. Add decorative cushion prices (Kırlent 1, 2, 3) with proper quantity calculation
      [currentPillows.pillow1, currentPillows.pillow2, currentPillows.pillow3].forEach((pillow, index) => {
        if (pillow.cushion && pillow.cushion !== 'Yok' && pillow.quantity > 0) {
          // Map cushion sizes to Kırlent types for pricing lookup
          let cushionKey = pillow.cushion;
          if (cushionKey === '50*50') cushionKey = 'Kırlent 1';
          else if (cushionKey === '60*60') cushionKey = 'Kırlent 2';
          else if (cushionKey === '60*45') cushionKey = 'Kırlent 3';

          if (pricingData.cushions && pricingData.cushions[cushionKey]) {
            const cushionData = pricingData.cushions[cushionKey];

            // Calculate cushion price per piece
            const cushionUnitPrice = cushionData.price || 0;

            // Get fabric price for this specific cushion based on selected kartela
            let cushionFabricPricePerMeter = 0;
            if (pricingData.fabrics && pricingData.fabrics[pillow.fabric]) {
              cushionFabricPricePerMeter = pricingData.fabrics[pillow.fabric][pillow.color] || 0;
            }

            // Calculate fabric cost per unit
            const cushionFabricCostPerUnit = (cushionData.fabricAmount || 0) * cushionFabricPricePerMeter;

            // Total cost per cushion unit
            const cushionTotalPerUnit = cushionUnitPrice + cushionFabricCostPerUnit;

            // Multiply by quantity
            const cushionTotalCost = cushionTotalPerUnit * pillow.quantity;
            totalPrice += cushionTotalCost;
            totalPillowCost += cushionTotalCost;

            console.log(`Cushion ${index + 1} (${cushionKey}): Unit price ${cushionUnitPrice}, Fabric per unit ${cushionFabricCostPerUnit}, Quantity ${pillow.quantity}, Total ${cushionTotalCost}`);
          }
        }
      });

      // Note: Bergere calculations are now handled above in the conditional block

      // 10. Apply profit rate from cost management
      const baseProfitRate = pricingData.profitRate || 0;
      totalPrice = totalPrice * (1 + baseProfitRate / 100);

      // 11. Apply store-specific profit margin (if available from store settings)
      // This would typically come from the authenticated store's settings
      // For now, we'll use a default or fetch from localStorage/auth context
      const storeSettings = JSON.parse(localStorage.getItem('storeSettings') || '{}');
      const storeProfitMargin = storeSettings.profitMargin || 0;
      if (storeProfitMargin > 0) {
        totalPrice = totalPrice * (1 + storeProfitMargin / 100);
        console.log(`Applied store profit margin: ${storeProfitMargin}%`);
      }

      console.log('Final calculated price:', totalPrice);
      console.log('Price calculation breakdown:', {
        furnitureType: currentDesign.furnitureType,
        basePrice: currentDesign.furnitureType === 'armchair' ? pricingData.generalOutcomeArmchair : pricingData.generalOutcomeBergere,
        fabricPricePerMeter,
        totalPrice
      });

      // Update price breakdown
      const lowerFrameBasePrice = pricingData.lowerFrameFixedPrice || 0;
      let lowerFrameColorMultiplier = 1;
      if (pricingData.lowerFrame && pricingData.lowerFrame[currentDesign.frame.color]) {
        lowerFrameColorMultiplier = pricingData.lowerFrame[currentDesign.frame.color];
      }
      const framePrice = lowerFrameBasePrice * lowerFrameColorMultiplier;

      const fabricPrice = fabricPricePerMeter * 5; // Approximate total fabric usage

      const legNumber = currentDesign.leg.type.split(' ')[0]; // Extract "1" from "1 Ahşap"
      const legPrice = pricingData.legOptions?.[legNumber]?.[currentDesign.leg.color] || 0;
      const cushionPrice = totalPillowCost; // Use the calculated pillow cost
      let additionalOptionsPrice = 0;

      // Calculate additional options price
      if (currentDesign.arm.hasKulak && pricingData.armrestBase?.['4']) {
        additionalOptionsPrice += pricingData.armrestBase['4'].price || 0;
      }
      if (currentDesign.arm.hasPapel && pricingData.armrestPapel?.['3']) {
        additionalOptionsPrice += pricingData.armrestPapel['3'].price || 0;
      }
      if (currentDesign.arm.hasFrontFlap && pricingData.armrestKlapa?.['5']) {
        additionalOptionsPrice += pricingData.armrestKlapa['5'].price || 0;
      }

      const skeletonPrice = currentDesign.furnitureType === 'bergere' ?
        (pricingData.bergereOptions?.[currentDesign.skeleton?.type || 'Bergere 1']?.price || 0) : 0;

      setPriceBreakdown({
        framePrice,
        fabricPrice,
        legPrice,
        cushionPrice,
        additionalOptionsPrice,
        skeletonPrice,
        totalPrice,
      });
    } catch (error) {
      console.error("Error calculating price:", error);
      // Set fallback price based on furniture type
      const fallbackPrice = currentDesign?.furnitureType === 'bergere' ? 1000 : 2000;
      setPriceBreakdown((prev) => ({
        ...prev,
        totalPrice: fallbackPrice,
      }));
    }
  }, [design, pillows]); // Remove pricingData to avoid circular updates

  // Immediately calculate price when prices are loaded
  useEffect(() => {
    if (Object.keys(allPartPrices).length > 0 &&
      (Object.keys(allPartPrices.frames).length > 0 ||
        Object.keys(allPartPrices.fabrics).length > 0)) {
      console.log('Prices loaded, calculating initial price');
      calculateTotalPrice(pricingData, design, pillows);

      // If saved design was loaded but price wasn't calculated yet, calculate it now
      if (savedDesignLoaded) {
        setTimeout(() => {
          console.log('Recalculating price for saved design after prices loaded');
          calculateTotalPrice(pricingData, design, pillows);
        }, 200);
      }
    }
  }, [allPartPrices, savedDesignLoaded, design, pillows, calculateTotalPrice]); // Added dependencies

  // Fetch store settings for profit margin calculation
  useEffect(() => {
    const fetchStoreSettings = async () => {
      try {
        const settings = await apiService.get('/StoreSettings');
        localStorage.setItem('storeSettings', JSON.stringify(settings));
        console.log('Store settings loaded:', settings);

        // Recalculate price with store settings
        if (Object.keys(pricingData).length > 0) {
          calculateTotalPrice(pricingData, design, pillows);
        }
      } catch (error) {
        console.warn('Could not fetch store settings:', error);
      }
    };

    fetchStoreSettings();
  }, []); // Run once on component mount

  // Toggle fullscreen
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    if (!isFullscreen) {
      // Find the container element that wraps the Babylon scene
      const sceneContainer = document.querySelector('.relative.w-full.h-full');
      if (sceneContainer instanceof HTMLElement && sceneContainer?.requestFullscreen) {
        sceneContainer.requestFullscreen().catch((err) => {
          console.error('Error attempting to enable fullscreen:', err);
        });
      } else {
        console.warn('Could not find scene container element for fullscreen');
      }
    } else {
      if (document.fullscreenElement && document.exitFullscreen) {
        document.exitFullscreen().catch((err) => {
          console.error('Error attempting to exit fullscreen:', err);
        });
      }
    }
  };

  // Capture 3D scene as image
  const capture3DScene = (): Promise<string> => {
    return new Promise((resolve) => {
      try {
        // Find the canvas element that contains the 3D scene
        const canvasElement = document.querySelector('canvas') as HTMLCanvasElement;
        if (canvasElement) {
          // Convert canvas to data URL
          const dataURL = canvasElement.toDataURL('image/png');
          resolve(dataURL);
        } else {
          // Fallback to placeholder image
          resolve('/logo.png');
        }
      } catch (error) {
        console.error('Error capturing 3D scene:', error);
        resolve('/logo.png');
      }
    });
  };

  // Other handlers
  const handlePrint = async () => {
    const printWindow = window.open('', '_blank', 'width=1024,height=768');
    if (!printWindow) return;

    // Capture the 3D scene
    const sceneImage = await capture3DScene();

    // Format currency
    const formatCurrency = (amount: number): string => {
      return new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: 'TRY'
      }).format(amount);
    };

    // Get furniture type in Turkish
    const furnitureTypeText = design.furnitureType === 'armchair' ? 'Koltuk' : 'Berjer';

    // Create summary of furniture configuration
    const generateSummary = () => {
      return `
        <div class="design-summary">
          <div class="design-image" style="text-align: center; margin-bottom: 20px;">
            <img src="/logo.png" alt="Kapsül Mobilya" style="max-width: 150px; margin-bottom: 15px;">
            <div class="furniture-preview" style="border: 1px solid #ddd; border-radius: 8px; padding: 10px; margin: 10px 0; background: #f9f9f9;">
              <img src="${sceneImage}" alt="3D Furniture Preview" style="max-width: 100%; height: 300px; object-fit: contain; border-radius: 5px; background: white;">
            </div>
          </div>
          <h3 style="margin: 15px 0 10px 0;">Tasarım ve Fiyat Detayları</h3>
          <table class="combined-table">
            <tr class="section-header">
              <th colspan="2">Tasarım Özeti</th>
              <th>Fiyat</th>
            </tr>
            <tr>
              <td><strong>Müşteri:</strong></td>
              <td>${customerInfo.firstName} ${customerInfo.lastName}</td>
              <td rowspan="8" style="vertical-align: top; padding: 10px;">
                <table class="price-breakdown" style="width: 100%;">
                  <tr>
                    <td>Kasa</td>
                    <td style="text-align: right;">${formatCurrency(priceBreakdown.framePrice)}</td>
                  </tr>
                  <tr>
                    <td>Kumaş</td>
                    <td style="text-align: right;">${formatCurrency(priceBreakdown.fabricPrice)}</td>
                  </tr>
                  <tr>
                    <td>Ayaklar</td>
                    <td style="text-align: right;">${formatCurrency(priceBreakdown.legPrice)}</td>
                  </tr>
                  <tr>
                    <td>Kırlentler</td>
                    <td style="text-align: right;">${formatCurrency(priceBreakdown.cushionPrice)}</td>
                  </tr>
                  <tr>
                    <td>Ek Seçenekler</td>
                    <td style="text-align: right;">${formatCurrency(priceBreakdown.additionalOptionsPrice)}</td>
                  </tr>
                  <tr class="total-row" style="border-top: 2px solid #333; font-weight: bold;">
                    <td>TOPLAM</td>
                    <td style="text-align: right; font-size: 1.2em;">${formatCurrency(priceBreakdown.totalPrice)}</td>
                  </tr>
                </table>
              </td>
            </tr>
            <tr>
              <td><strong>Ürün:</strong></td>
              <td>${furnitureTypeText}</td>
            </tr>
            <tr>
              <td><strong>Kumaş:</strong></td>
              <td>${design.fabric.type} - ${design.fabric.color}</td>
            </tr>
            <tr>
              <td><strong>Kol Tipi:</strong></td>
              <td>${design.arm.type}</td>
            </tr>
            <tr>
              <td><strong>Ayak Tipi:</strong></td>
              <td>${design.leg.type}</td>
            </tr>
            <tr>
              <td><strong>Oturum:</strong></td>
              <td>${design.seat.type}</td>
            </tr>
            <tr>
              <td><strong>Kırlentler:</strong></td>
              <td>${pillows.backPillow}, ${pillows.pillow1.cushion}</td>
            </tr>
            <tr>
              <td><strong>Barkod:</strong></td>
              <td>${barcodeText}</td>
            </tr>
          </table>
        </div>
      `;
    };

    const htmlContent = `
      <html><head><title>Mobilya Tasarım Bilgileri</title>
      <style>
        body { font-family: Arial, sans-serif; padding: 15px; margin: 0; }
        h2 { margin-top: 0; color: #333; font-size: 20px; text-align: center; }
        h3 { color: #555; font-size: 16px; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 15px; }
        td, th { border: 1px solid #ddd; padding: 8px; }
        th { background-color: #f5f5f5; text-align: left; font-weight: bold; }
        .combined-table { margin-top: 10px; }
        .combined-table td { padding: 6px 10px; }
        .section-header th { background-color: #e0e0e0; text-align: center; font-size: 14px; }
        .price-breakdown { border: none; }
        .price-breakdown td { border: none; padding: 4px 8px; font-size: 13px; }
        .price-breakdown tr:nth-child(even) { background-color: #f9f9f9; }
        .total-row { background-color: #f0f0f0; }
        .content-container { max-width: 800px; margin: 0 auto; }
        .footer { margin-top: 20px; font-size: 0.8em; color: #777; text-align: center; }
        @media print {
          body { padding: 10px; }
          @page { size: A4; margin: 1cm; }
          .content-container { max-width: 100%; }
        }
      </style>
      </head><body>
      <div class="content-container">
        <h2>Kapsül Mobilya - Tasarım Detayları</h2>

        ${generateSummary()}

        <div class="footer">
          <p>Tarih: ${new Date().toLocaleDateString('tr-TR')} | Kapsül Mobilya &copy; ${new Date().getFullYear()}</p>
        </div>
      </div>
      </body></html>`;

    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => printWindow.print(), 500);
  };

  const handleSaveDesign = async () => {
    try {
      // Prepare the design data to save in the format expected by backend
      const cartDesign = {
        // Same structure as handleAddToCart
        armchairType: design.furnitureType,
        baseFrame: '1', // Always use the single AltKasa file
        legs: design.leg.type,
        legFabric: {
          option: design.leg.type,
          color: design.leg.color
        },
        woodVeneer: '1', // Default wood veneer - separate from Alt Kasa
        woodVeneerFabric: {
          option: '1', // Default wood veneer option
          color: design.frame.color
        },
        mainAhsapFabric: {
          option: '1', // Fixed option for single AltKasa
          color: design.frame.color
        },
        armrest: {
          main: design.arm.type.replace('KOL ', ''), // Extract number from "KOL 1"
          sub: [
            ...(design.arm.hasPapel ? ['3'] : []),
            ...(design.arm.hasKulak ? ['4'] : []),
            ...(design.arm.hasFrontFlap ? ['5'] : [])
          ].join('-') || 'BOŞ',
          mainAhsap: false
        },
        cushions: [
          // Convert pillows to cushion array format
          ...(pillows.pillow1.cushion && pillows.pillow1.cushion !== 'Yok' ? [{
            cushionType: pillows.pillow1.cushion,
            fabric: pillows.pillow1.fabric,
            color: pillows.pillow1.color
          }] : []),
          ...(pillows.pillow2.cushion && pillows.pillow2.cushion !== 'Yok' ? [{
            cushionType: pillows.pillow2.cushion,
            fabric: pillows.pillow2.fabric,
            color: pillows.pillow2.color
          }] : []),
          ...(pillows.pillow3.cushion && pillows.pillow3.cushion !== 'Yok' ? [{
            cushionType: pillows.pillow3.cushion,
            fabric: pillows.pillow3.fabric,
            color: pillows.pillow3.color
          }] : [])
        ],
        backPillow: pillows.backPillow.type,
        sharedFabric: {
          option: design.fabric.type,
          color: design.fabric.color
        },
        seat: {
          option: design.seat.type,
          sub: 'BOŞ'
        },
        customerName: customerInfo.firstName,
        customerSurname: customerInfo.lastName,
        totalPrice: priceBreakdown.totalPrice,
        barcode: barcodeText,
        model: design.furnitureType
      };

      const savedDesign = {
        name: `${customerInfo.firstName} ${customerInfo.lastName} - ${design.furnitureType}`.trim() || 'Custom Design',
        designJson: JSON.stringify(cartDesign),
        furnitureType: design.furnitureType === 'armchair' ? 'armchair' : 'bergere',
        totalPrice: priceBreakdown.totalPrice
      };

      await backendService.saveDesign(savedDesign);
      toast.success(t('designSaved'));

      // Navigate to saved designs page
      setTimeout(() => {
        navigate('/store/saved-designs');
      }, 1500);
    } catch (err) {
      toast.error(t('designSaveFailed'));
      console.error('Save design error:', err);
    }
  };

  const handleAddToCart = async () => {
    try {
      // Check if customer name is provided
      if (!customerInfo.firstName || !customerInfo.lastName) {
        toast.error(t('pleaseEnterCustomerName'));
        return;
      }

      // Prepare the design for cart with backend expected field names
      let cartDesign: any = {
        // Common fields for both armchair and bergere
        armchairType: design.furnitureType,
        sharedFabric: {
          option: design.fabric.type,
          color: design.fabric.color
        },
        customerName: customerInfo.firstName,
        customerSurname: customerInfo.lastName,
        totalPrice: priceBreakdown.totalPrice,
        barcode: barcodeText,
        model: design.furnitureType
      };

      if (design.furnitureType === 'bergere') {
        // Bergere-specific fields with all required fields for backend validation
        cartDesign = {
          ...cartDesign,
          // Required fields for bergere (using appropriate defaults)
          legs: '1', // Default legs type for bergere
          baseFrame: '1', // Default base frame for bergere
          woodVeneer: '1', // Default wood veneer for bergere
          legFabric: {
            option: design.skeleton?.type || '1',
            color: design.skeleton?.color || 'CevizAhsap'
          },
          woodVeneerFabric: {
            option: design.skeleton?.type || '1',
            color: design.skeleton?.color || 'CevizAhsap'
          },
          mainAhsapFabric: {
            option: design.skeleton?.type || '1',
            color: design.skeleton?.color || 'CevizAhsap'
          },
          armrest: {
            main: '1', // Default armrest main for bergere
            sub: 'BOŞ', // Default sub value
            mainAhsap: false // Default mainAhsap value
          },
          seat: {
            option: '1', // Default seat option for bergere
            sub: 'BOŞ' // Default sub value
          },
          // Bergere-specific skeleton information
          skeleton: {
            type: design.skeleton?.type || '1',
            color: design.skeleton?.color || 'CevizAhsap'
          }
        };
      } else {
        // Armchair-specific fields
        cartDesign = {
          ...cartDesign,
          baseFrame: '1', // Always use the single AltKasa file
          legs: design.leg.type,
          legFabric: {
            option: design.leg.type,
            color: design.leg.color
          },
          woodVeneer: '1', // Default wood veneer - separate from Alt Kasa
          woodVeneerFabric: {
            option: '1', // Default wood veneer option
            color: design.frame.color
          },
          mainAhsapFabric: {
            option: '1', // Fixed option for single AltKasa
            color: design.frame.color
          },
          armrest: {
            main: design.arm.type,
            sub: 'BOŞ', // Default value for sub
            mainAhsap: false // Default value for mainAhsap
          },
          seat: {
            option: design.seat.type,
            sub: 'BOŞ' // Default value for sub
          }
        };
      }

      // Add cushions for both types
      cartDesign.cushions = [
        // Convert pillows to cushion array format expected by backend
        ...(pillows.pillow1.cushion && pillows.pillow1.cushion !== 'Yok' ? [{
          cushionType: pillows.pillow1.cushion,
          fabric: pillows.pillow1.fabric,
          color: pillows.pillow1.color,
          quantity: pillows.pillow1.quantity
        }] : []),
        ...(pillows.pillow2.cushion && pillows.pillow2.cushion !== 'Yok' ? [{
          cushionType: pillows.pillow2.cushion,
          fabric: pillows.pillow2.fabric,
          color: pillows.pillow2.color,
          quantity: pillows.pillow2.quantity
        }] : []),
        ...(pillows.pillow3.cushion && pillows.pillow3.cushion !== 'Yok' ? [{
          cushionType: pillows.pillow3.cushion,
          fabric: pillows.pillow3.fabric,
          color: pillows.pillow3.color,
          quantity: pillows.pillow3.quantity
        }] : [])
      ];

      cartDesign.backPillow = pillows.backPillow.type;
      cartDesign.backPillowQuantity = pillows.backPillow.quantity;

      console.log('Sending cart design:', cartDesign);

      await backendService.addCustomDesignToCart(cartDesign);
      toast.success(t('addedToCart'));

      // Refresh the cart after adding the item
      if (typeof fetchCart === 'function') {
        fetchCart();
      }
    } catch (err) {
      toast.error(t('addToCartFailed'));
      console.error('Cart error:', err);
    }
  };

  // Fetch all prices on component mount
  useEffect(() => {
    fetchAllPartPrices();
  }, [fetchAllPartPrices]);

  // Helper function to parse arm sub options
  const parseArmSubOptions = (sub: string | string[] | undefined) => {
    const subStr = Array.isArray(sub) ? sub.join('-') : (sub || '');
    return {
      hasPapel: subStr.includes('3'),
      hasKulak: subStr.includes('4'),
      hasFrontFlap: subStr.includes('5')
    };
  };

  // Load saved design if provided
  useEffect(() => {
    if (location.state?.savedDesign) {
      const saved = location.state.savedDesign;
      console.log('Loading saved design:', saved);

      // Update customer info if available
      if (saved.customerName || saved.customerSurname) {
        setCustomerInfo({
          firstName: saved.customerName || '',
          lastName: saved.customerSurname || ''
        });
      }

      // Update design state based on saved design structure
      if (saved.model || saved.furnitureType || saved.armchairType) {
        console.log('Full saved design data:', saved);
        console.log('Updating design from saved data:', {
          fabric: saved.sharedFabric,
          frame: { woodVeneer: saved.woodVeneer, baseFrame: saved.baseFrame, woodVeneerFabric: saved.woodVeneerFabric },
          arm: { armrest: saved.armrest, mainAhsapFabric: saved.mainAhsapFabric },
          leg: { legs: saved.legs, legFabric: saved.legFabric },
          seat: saved.seat
        });

        setDesign(prevDesign => ({
          ...prevDesign,
          furnitureType: saved.model || saved.furnitureType || 'armchair',
          fabric: {
            type: saved.sharedFabric?.option || saved.fabric?.type || 'cartela1',
            color: saved.sharedFabric?.color || saved.fabric?.color || '1'
          },
          frame: {
            type: saved.baseFrame || saved.frame?.type || '1', // Use baseFrame for Alt Kasa, not woodVeneer
            color: saved.woodVeneerFabric?.option || saved.woodVeneerFabric?.color || saved.frame?.color || 'CevizAhsap'
          },
          arm: {
            type: saved.armrest?.main ? `KOL ${saved.armrest.main}` : saved.arm?.type || 'KOL 1',
            color: saved.mainAhsapFabric?.option || saved.mainAhsapFabric?.color || saved.arm?.color || 'CevizAhsap',
            ...parseArmSubOptions(saved.armrest?.sub)
          },
          seat: {
            type: saved.seat?.option || saved.seat?.type || '1',
            color: saved.sharedFabric?.color || saved.seat?.color || 'brown',
            options: saved.seat?.options || {
              cektirme: false,
              tekParca: false,
              bombe: false,
              kirisiklik: false,
              kulak: false
            }
          },
          leg: {
            type: saved.legs || saved.leg?.type || '1 Ahşap',
            color: saved.legFabric?.option || saved.legFabric?.color || saved.leg?.color || 'CevizAhsap'
          }
        }));

        // Update babylon design
        setBabylonDesign(prevDesign => ({
          ...prevDesign,
          furnitureType: saved.model || saved.furnitureType || 'armchair',
          frameColor: saved.woodVeneerFabric?.option || 'CevizAhsap',
          upholstery: saved.sharedFabric?.option || 'brown',
          legType: saved.legs || '1 Ahşap',
          sharedFabric: saved.sharedFabric?.option || 'brown',
          legs: saved.legs ? saved.legs.split(' ')[0] : '1',
          legFabric: saved.legFabric?.option || 'CevizAhsap',
          lowerFrame: saved.baseFrame || '1',
          woodVeneer: '1', // Default wood veneer - separate from Alt Kasa
          woodVeneerFabric: saved.woodVeneerFabric?.option || 'CevizAhsap',
          armrest: {
            main: saved.armrest?.main || '1',
            sub: saved.armrest?.sub || []
          },
          seat: {
            type: saved.seat?.option || '1',
            color: saved.sharedFabric?.color || 'brown',
            options: saved.seat?.options || {
              cektirme: false,
              tekParca: false,
              bombe: false,
              kirisiklik: false,
              kulak: false
            }
          },
          fabric: {
            type: saved.sharedFabric?.option || 'cartela1',
            color: saved.sharedFabric?.color || '1'
          }
        }));

        // Update pillows if available
        if (saved.backPillow || saved.cushions) {
          setPillows({
            backPillow: saved.backPillow || 'Silikon Elyaf',
            pillow1: saved.cushions?.[0]?.cushionType || '50*50',
            pillow2: saved.cushions?.[1]?.cushionType || '50*50',
            pillow3: saved.cushions?.[2]?.cushionType || '50*50'
          });
        }

        // Mark that saved design has been loaded
        setSavedDesignLoaded(true);

        // If prices are already loaded, calculate the price immediately
        if (Object.keys(allPartPrices.frames).length > 0 || Object.keys(allPartPrices.fabrics).length > 0) {
          setTimeout(() => {
            console.log('Calculating price after loading saved design');
            // Use the design state that was just set in the setDesign call above
            const updatedDesign = {
              ...design,
              furnitureType: saved.model || saved.furnitureType || 'armchair',
              fabric: {
                type: saved.sharedFabric?.option || saved.fabric?.type || 'cartela1',
                color: saved.sharedFabric?.color || saved.fabric?.color || '1'
              },
              frame: {
                type: saved.baseFrame || saved.frame?.type || '1', // Use baseFrame for Alt Kasa, not woodVeneer
                color: saved.woodVeneerFabric?.option || saved.woodVeneerFabric?.color || saved.frame?.color || 'CevizAhsap'
              },
              arm: {
                type: saved.armrest?.main ? `KOL ${saved.armrest.main}` : saved.arm?.type || 'KOL 1',
                color: saved.mainAhsapFabric?.option || saved.mainAhsapFabric?.color || saved.arm?.color || 'CevizAhsap',
                ...parseArmSubOptions(saved.armrest?.sub)
              },
              seat: {
                type: saved.seat?.option || saved.seat?.type || '1',
                color: saved.sharedFabric?.color || saved.seat?.color || 'brown',
                options: saved.seat?.options || {
                  cektirme: false,
                  tekParca: false,
                  bombe: false,
                  kirisiklik: false,
                  kulak: false
                }
              },
              leg: {
                type: saved.legs || saved.leg?.type || '1 Ahşap',
                color: saved.legFabric?.option || saved.legFabric?.color || saved.leg?.color || 'CevizAhsap'
              }
            };
            calculateTotalPrice(pricingData, updatedDesign, pillows);
          }, 100);
        }
      }
    }
  }, [location.state, allPartPrices, pillows, calculateTotalPrice]); // Added missing dependencies

  // Recalculate price when design or pillows change
  useEffect(() => {
    // Only calculate price if prices are loaded AND either it's not a saved design or the saved design has been loaded
    if ((Object.keys(allPartPrices.frames).length > 0 || Object.keys(allPartPrices.fabrics).length > 0) &&
      (!location.state?.savedDesign || savedDesignLoaded)) {
      console.log('Recalculating price due to design/pillows change', {
        hasPrices: Object.keys(allPartPrices.frames).length > 0,
        savedDesignLoaded,
        design
      });
      calculateTotalPrice(pricingData, design, pillows);
    }
  }, [design, pillows, calculateTotalPrice, allPartPrices, savedDesignLoaded, location.state]);

  // Call updateBabylonDesign on initial load and when design changes
  useEffect(() => {
    updateBabylonDesign();
  }, [
    design.furnitureType,
    design.fabric,
    design.frame,
    design.arm,
    design.seat,
    design.leg,
    pillows.backPillow.type,
    updateBabylonDesign
  ]);

  // Helper function to get fabric code
  const getFabricCode = (fabricType: string): string => {
    const fabricCodes: Record<string, string> = {
      'brown': 'BR',
      'greenCotton': 'GC',
      'grey': 'GR',
      'cartela1': 'K1',
      'cartela2': 'K2',
      'cartela3': 'K3',
      'cartela4': 'K4',
      'cartela5': 'K5',
      'cartela6': 'K6',
      'cartela7': 'K7',
      'cartela8': 'K8',
      'cartela9': 'K9',
      'cartela10': 'K10',
      'cartela11': 'K11',
      'cartela12': 'K12',
    };
    return fabricCodes[fabricType] || fabricType;
  };

  // Helper function to get wood color index
  const getWoodColorIndex = (color: string): string => {
    const colorIndices: Record<string, string> = {
      'CevizAhsap': '1',
      'BeyazAhsap': '2',
      'EkruAhsap': '3',
      'GriAhsap': '4',
      'AntrasitAhsap': '5',
      'SariEskitmeAhsap': '6',
      'GriEskitmeAhsap': '7',
      'CevizEskitmeAhsap': '8',
    };
    return colorIndices[color] || '1';
  };

  // Helper function to get metal color index
  const getMetalColorIndex = (color: string): string => {
    const colorIndices: Record<string, string> = {
      'Bronz': '9',
      'Gold': '10',
      'nickel': '11',
    };
    return colorIndices[color] || color;
  };

  // Add useEffect to update barcode when design changes
  useEffect(() => {
    // Get fabric code
    const fabricCode = getFabricCode(design.fabric.type);

    // Get arm options with correct numbering
    const armOptions = [];
    if (design.arm.hasDugme) armOptions.push('1'); // 1 = Düğme Kapiton
    if (design.arm.hasBoğumKapiton) armOptions.push('2'); // 2 = Boğum Kapiton
    if (design.arm.hasPapel) armOptions.push('3'); // 3 = Papel
    if (design.arm.hasKulak) armOptions.push('4'); // 4 = Kulak
    if (design.arm.hasFrontFlap) armOptions.push('5'); // 5 = Ön Klapa

    const armOptionsStr = armOptions.length > 0 ? armOptions.join('-') : '';

    // Get seat options (matching available assets: 1, 3, 4)
    const seatOptions = [];
    if (design.seat.options.cektirme) seatOptions.push('1'); // 1 = Çektirme
    if (design.seat.options.tekParca) seatOptions.push('3'); // 3 = Tek Parça
    if (design.seat.options.bombe) seatOptions.push('4'); // 4 = Bombe
    // Note: kirisiklik (option 2) and kulak are not included as they don't exist in assets
    const seatOptionsStr = seatOptions.length > 0 ? seatOptions.join('-') : '';

    // Extract arm number from "KOL X" format
    const armNumber = design.arm.type.replace('KOL ', '');

    // Extract leg number (e.g., "1 Ahşap" -> "1", "3 Metal" -> "3")
    const legNumber = design.leg.type.split(' ')[0];

    // Get color indices
    const frameColorIndex = getWoodColorIndex(design.frame.color);
    const legColorIndex = design.leg.color.includes('Ahsap')
      ? getWoodColorIndex(design.leg.color)
      : getMetalColorIndex(design.leg.color);

    // Build barcode for armchair
    let barcodeString = '';
    if (design.furnitureType === 'armchair') {
      // For fabric code: if it's a kartela, just show the number part (K1 -> 1, K2 -> 2, etc.)
      let fabricPart = '';
      if (design.fabric.type.startsWith('cartela')) {
        // Extract number from cartela (cartela1 -> 1, cartela12 -> 12)
        const cartelaNumber = design.fabric.type.replace('cartela', '');
        fabricPart = `${cartelaNumber}(${design.fabric.color})`;
      } else {
        // For other fabrics, show code and color
        fabricPart = `${fabricCode}(${design.fabric.color})`;
      }

      // Format: Fabric_Arm(Options)-Seat(Options)-Frame(ColorIndex)-LegNumber-LegColorIndex
      barcodeString = `${fabricPart}_${armNumber}${armOptionsStr ? `(${armOptionsStr})` : ''}-${design.seat.type}${seatOptionsStr ? `(${seatOptionsStr})` : ''}-${design.frame.type}(${frameColorIndex})-${legNumber}-${legColorIndex}`;
    } else if (design.furnitureType === 'bergere') {
      // For bergere: simpler format without arm and frame
      let fabricPart = '';
      if (design.fabric.type.startsWith('cartela')) {
        const cartelaNumber = design.fabric.type.replace('cartela', '');
        fabricPart = `${cartelaNumber}(${design.fabric.color})`;
      } else {
        fabricPart = `${fabricCode}(${design.fabric.color})`;
      }

      const skeletonColorIndex = design.skeleton ? getWoodColorIndex(design.skeleton.color) : '1';
      barcodeString = `B${fabricPart}_${design.skeleton?.type || '1'}-${skeletonColorIndex}`;
    }

    setBarcodeText(barcodeString);
  }, [design]); // Only depend on design, not pillows

  // Add useEffect to handle model loading state
  useEffect(() => {
    // Start with loading state
    setIsModelLoading(true);

    // Set a timeout to hide the loading indicator after a reasonable time
    const loadingTimer = setTimeout(() => {
      setIsModelLoading(false);
    }, 3000); // 3 seconds should be enough for most models to load

    // Clean up the timer
    return () => clearTimeout(loadingTimer);
  }, [babylonDesign]); // Re-run when the 3D model design changes

  // Format price to Turkish Lira format
  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    }).format(price);
  };

  // Memoize the decorative cushions prop to prevent re-renders
  const memoizedDecorativeCushions = React.useMemo(() => ({
    size: "50*50",
    fabric: "static",
    quantity: 1,
  }), []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
      <main className="relative w-full mx-auto py-4 px-8">
        <h2 className="text-3xl font-extrabold mb-4 bg-gradient-to-r">{t('furnitureDesignTool')}</h2>

        {/* Furniture Type Selection */}
        <div className="space-y-4 p-4 bg-white/90 backdrop-blur-sm border border-gray-100 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
          <h3 className="text-xl font-bold text-transparent -mt-2 bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600">{t('furnitureType')}</h3>
          <select
            className="w-full bg-white border border-gray-300 text-gray-900 placeholder-gray-400 focus:border-purple-500 focus:ring-purple-500/20 transition-all duration-300 rounded-lg py-2.5 pl-3 pr-10"
            value={design.furnitureType}
            onChange={handleFurnitureTypeChange}
          >
            <option value="armchair">{t('armchair')}</option>
            <option value="bergere">{t('bergere')}</option>
          </select>
        </div>

        {/* Different layout for bergere vs armchair */}
        {design.furnitureType === 'bergere' ? (
          // Bergere Layout: Controls at top, scenes below
          <div className="space-y-6 my-3">
            {/* Top Controls for Bergere */}
            <div className="bg-white/90 backdrop-blur-sm border border-gray-100 rounded-lg shadow-sm p-4">
              <div className="grid grid-cols-3 gap-6">
                {/* Fabric section */}
                <div>
                  <h3 className="text-lg font-semibold mb-3 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('fabric')}</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('selectCatalog')}</label>
                      <select
                        className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                        value={design.fabric.type}
                        onChange={handleFabricTypeChange}
                      >
                        <option value="cartela1">Kartela 1</option>
                        <option value="cartela2">Kartela 2</option>
                        <option value="cartela3">Kartela 3</option>
                        <option value="cartela4">Kartela 4</option>
                        <option value="cartela5">Kartela 5</option>
                        <option value="cartela6">Kartela 6</option>
                        <option value="cartela7">Kartela 7</option>
                        <option value="cartela8">Kartela 8</option>
                        <option value="cartela9">Kartela 9</option>
                        <option value="cartela10">Kartela 10</option>
                        <option value="cartela11">Kartela 11</option>
                        <option value="cartela12">Kartela 12</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('selectColor')}</label>
                      <select
                        className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                        value={design.fabric.color}
                        onChange={handleFabricColorChange}
                      >
                        {/* Show different number of colors based on selected cartela */}
                        {design.fabric.type === 'cartela12' ? (
                          // Cartela 12 has 6 colors
                          Array.from({ length: 6 }, (_, i) => (
                            <option key={i + 1} value={String(i + 1)}>{i + 1}</option>
                          ))
                        ) : design.fabric.type === 'cartela8' ? (
                          // Cartela 8 has 10 colors
                          Array.from({ length: 10 }, (_, i) => (
                            <option key={i + 1} value={String(i + 1)}>{i + 1}</option>
                          ))
                        ) : (
                          // All other cartelas have 9 colors
                          Array.from({ length: 9 }, (_, i) => (
                            <option key={i + 1} value={String(i + 1)}>{i + 1}</option>
                          ))
                        )}
                      </select>
                    </div>
                  </div>
                </div>

                {/* Skeleton Color */}
                <div>
                  <h3 className="text-lg font-semibold mb-3 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('skeletonColor')}</h3>
                  <div>
                    <label className="block text-sm font-medium mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('selectSkeletonColor')}</label>
                    <select
                      className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      value={design.skeleton?.color || 'CevizAhsap'}
                      onChange={(e) => {
                        setDesign(prev => ({
                          ...prev,
                          skeleton: {
                            ...prev.skeleton!,
                            color: e.target.value
                          }
                        }));
                      }}
                    >
                      <option value="CevizAhsap">Ceviz</option>
                      <option value="BeyazAhsap">Beyaz</option>
                      <option value="EkruAhsap">Ekru</option>
                      <option value="GriAhsap">Gri</option>
                      <option value="AntrasitAhsap">Antrasit</option>
                      <option value="SariEskitmeAhsap">Sarı Eskitme</option>
                      <option value="GriEskitmeAhsap">Gri Eskitme</option>
                      <option value="CevizEskitmeAhsap">Ceviz Eskitme</option>
                    </select>
                  </div>
                </div>

                {/* Price Display */}
                <div className="flex items-end">
                  <div className="w-full">
                    <h3 className="text-lg font-semibold mb-3 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('price')}</h3>
                    <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-3">
                      <span className="text-lg font-medium text-gray-700">
                        {t('totalPrice')}:
                        {isLoadingPrice ? (
                          <span className="text-xl text-gray-400 block">{t('loading')}</span>
                        ) : (
                          <span className="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent block">
                            {formatPrice(priceBreakdown.totalPrice)}
                          </span>
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 3D Skeleton Scenes */}
            <div className="space-y-4">
              <h3 className="text-xl font-bold text-center text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600">
                {t('skeletonOptions')}
              </h3>
              <div className="grid grid-cols-3 gap-4">
                {['1', '2', '3'].map((skeletonType) => (
                  <div
                    key={skeletonType}
                    className={`relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl shadow-lg overflow-hidden border-2 cursor-pointer transition-all duration-300 ${selectedSkeleton === skeletonType
                      ? 'border-purple-500 shadow-purple-200'
                      : 'border-gray-200 hover:border-purple-300'
                      }`}
                    onClick={() => {
                      setSelectedSkeleton(skeletonType);
                      setDesign({ ...design, skeleton: { type: skeletonType, color: design.skeleton?.color || 'CevizAhsap' } });
                    }}
                  >
                    <div className="absolute top-2 left-2 z-10">
                      <span className={`px-2 py-1 rounded-lg text-sm font-medium ${selectedSkeleton === skeletonType
                        ? 'bg-purple-500 text-white'
                        : 'bg-white/90 text-gray-700'
                        }`}>
                        {t('skeleton')} {skeletonType}
                      </span>
                    </div>

                    <div className="h-[400px] w-full">
                      <MemoizedBabylonScene
                        design={{
                          ...babylonDesign,
                          furnitureType: 'bergere',
                          skeleton: { type: skeletonType, color: design.skeleton?.color || 'CevizAhsap' },
                          seat: {
                            type: skeletonType,
                            color: design.seat?.color || 'default',
                            options: design.seat?.options || {
                              cektirme: false,
                              tekParca: false,
                              bombe: false,
                              kirisiklik: false,
                              kulak: false
                            }
                          } // Use same number for seat
                        }}
                        furnitureType="bergere"
                        getPartFilePath={getPartFilePath}
                        backCushion="Silikon Elyaf"
                        decorativeCushions={memoizedDecorativeCushions}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          // Armchair Layout: Side-by-side layout
          <div className="grid grid-cols-1 md:grid-cols-24 gap-6 my-3">
            {/* 3D Model Viewer */}
            <div className="col-span-15">
              <div className="bg-white/90 backdrop-blur-sm border border-gray-100 rounded-lg shadow-sm p-4" style={{ height: '700px' }}>
                <div className="relative w-full h-full">
                  <div
                    className="relative w-full h-full"
                    onWheel={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                    onMouseEnter={(e) => {
                      // Prevent page scrolling when mouse is over the 3D scene
                      document.body.style.overflow = 'hidden';
                    }}
                    onMouseLeave={(e) => {
                      // Re-enable page scrolling when mouse leaves the 3D scene
                      document.body.style.overflow = 'auto';
                    }}
                    style={{ overflow: 'hidden' }}
                  >
                    <MemoizedBabylonScene
                      design={babylonDesign}
                      furnitureType={babylonDesign.furnitureType}
                      getPartFilePath={getPartFilePath}
                      backCushion="Silikon Elyaf"
                      decorativeCushions={memoizedDecorativeCushions}
                    />
                    {isModelLoading && (
                      <div className="absolute inset-0 flex items-center justify-center bg-white/50">
                        <div className="text-center">
                          <div className="w-12 h-12 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                          <p className="text-purple-600 font-medium">{t('handleModelLoading')}</p>
                        </div>
                      </div>
                    )}
                  </div>
                  <button
                    className="absolute top-4 right-4 p-2 bg-white/90 backdrop-blur-sm border border-gray-100 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 text-gray-700 hover:text-purple-600 z-10"
                    onClick={toggleFullscreen}
                    title={t('enterFullscreen')}
                  >
                    <BsFullscreen size={20} />
                  </button>
                </div>
              </div>

              {/* Price Display */}
              <div className="mt-1.5 flex items-center justify-end bg-white/90 backdrop-blur-sm border border-gray-100 rounded-lg p-3 shadow-sm">
                <span className="text-lg font-medium text-gray-700">
                  Toplam Fiyat:
                  {isLoadingPrice ? (
                    <span className="text-xl text-gray-400">Yükleniyor...</span>
                  ) : (
                    <span className="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      {formatPrice(priceBreakdown.totalPrice)}
                    </span>
                  )}
                </span>
              </div>
            </div>

            {/* Customization Panel */}
            <div className="space-y-4 p-4 bg-white/90 backdrop-blur-sm border border-gray-100 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 col-span-9">
              {/* Fabric section */}
              <div className="col-span-2">
                <h3 className="text-lg font-semibold mb-1 -mt-2 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Kumaş</h3>
                <div className="flex gap-4">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Kumaş Seç</label>
                    <select
                      className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md "
                      value={design.fabric.type}
                      onChange={handleFabricTypeChange}
                    >
                      <option value="cartela1">Kartela 1</option>
                      <option value="cartela2">Kartela 2</option>
                      <option value="cartela3">Kartela 3</option>
                      <option value="cartela4">Kartela 4</option>
                      <option value="cartela5">Kartela 5</option>
                      <option value="cartela6">Kartela 6</option>
                      <option value="cartela7">Kartela 7</option>
                      <option value="cartela8">Kartela 8</option>
                      <option value="cartela9">Kartela 9</option>
                      <option value="cartela10">Kartela 10</option>
                      <option value="cartela11">Kartela 11</option>
                      <option value="cartela12">Kartela 12</option>
                    </select>
                  </div>
                  <div className="flex-1">
                    <label className="block text-sm font-medium mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Renk Seç</label>
                    <select
                      className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      value={design.fabric.color}
                      onChange={handleFabricColorChange}
                    >
                      {/* Show different number of colors based on selected cartela */}
                      {design.fabric.type === 'cartela12' ? (
                        // Cartela 12 has 6 colors
                        Array.from({ length: 6 }, (_, i) => (
                          <option key={i + 1} value={String(i + 1)}>{i + 1}</option>
                        ))
                      ) : design.fabric.type === 'cartela8' ? (
                        // Cartela 8 has 10 colors
                        Array.from({ length: 10 }, (_, i) => (
                          <option key={i + 1} value={String(i + 1)}>{i + 1}</option>
                        ))
                      ) : (
                        // All other cartelas have 9 colors
                        Array.from({ length: 9 }, (_, i) => (
                          <option key={i + 1} value={String(i + 1)}>{i + 1}</option>
                        ))
                      )}
                    </select>
                  </div>
                </div>
              </div>

              {/* Arms section - Hide for bergere */}
              {design.furnitureType !== 'bergere' && (
                <div className="col-span-1 mt-1.5">
                  <h3 className="text-lg font-semibold mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('arm')}</h3>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('selectArmrest')}</label>
                    <select
                      className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md "
                      value={design.arm.type}
                      onChange={handleArmTypeChange}
                    >
                      <option value="KOL 1">{t('KOL 1')}</option>
                      <option value="KOL 2">{t('KOL 2')}</option>
                      <option value="KOL 3">{t('KOL 3')}</option>
                      <option value="KOL 4">{t('KOL 4')}</option>
                      <option value="KOL 5">{t('KOL 5')}</option>
                      <option value="KOL 6">{t('KOL 6')}</option>
                      <option value="KOL 7">{t('KOL 7')}</option>
                    </select>
                  </div>
                  <div className="mt-1.5 flex flex-col flex-wrap gap-y-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">{t('armOptions')}</label>
                      {/* Armrest options in correct order: 1=Düğme, 2=Boğum, 3=Papel, 4=Kulak, 5=Ön Klapa */}
                      <label className="inline-flex items-center mr-4">
                        <input
                          type="checkbox"
                          className="form-checkbox h-4 w-4 text-purple-600"
                          checked={design.arm.hasDugme || false}
                          onChange={(e) => handleArmOptionChange('hasDugme', e.target.checked)}
                        />
                        <span className="ml-2 text-sm text-gray-700">{t('button')}</span>
                      </label>

                      <label className="inline-flex items-center mr-4">
                        <input
                          type="checkbox"
                          className="form-checkbox h-4 w-4 text-purple-600"
                          checked={design.arm.hasBoğumKapiton || false}
                          onChange={(e) => handleArmOptionChange('hasBoğumKapiton', e.target.checked)}
                        />
                        <span className="ml-2 text-sm text-gray-700">{t('joint')}</span>
                      </label>

                      <label className="inline-flex items-center mr-4">
                        <input
                          type="checkbox"
                          className="form-checkbox h-4 w-4 text-purple-600"
                          checked={design.arm.hasPapel}
                          onChange={(e) => handleArmOptionChange('hasPapel', e.target.checked)}
                        />
                        <span className="ml-2 text-sm text-gray-700">{t('papel')}</span>
                      </label>
                      <label className="inline-flex items-center mr-4">
                        <input
                          type="checkbox"
                          className="form-checkbox h-4 w-4 text-purple-600"
                          checked={design.arm.hasKulak}
                          onChange={(e) => handleArmOptionChange('hasKulak', e.target.checked)}
                        />
                        <span className="ml-2 text-sm text-gray-700">{t('kulak')}</span>
                      </label>

                      <label className="inline-flex items-center mr-4">
                        <input
                          type="checkbox"
                          className="form-checkbox h-4 w-4 text-purple-600"
                          checked={design.arm.hasFrontFlap}
                          onChange={(e) => handleArmOptionChange('hasFrontFlap', e.target.checked)}
                        />
                        <span className="ml-2 text-sm text-gray-700">{t('frontFlap')}</span>
                      </label>
                      <div className="w-full"></div>
                    </div>
                  </div>
                </div>
              )}

              {/* Seat Options */}
              <div className="col-span-1 mt-1.5">
                <h3 className="text-lg font-semibold mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  {design.furnitureType === 'bergere' ? t('skeletonAndSeat') : t('seat')}
                </h3>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    {design.furnitureType === 'bergere' ? t('selectSkeletonColor') : t('selectSeat')}
                  </label>
                  <select
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={design.seat.type}
                    onChange={handleSeatTypeChange}
                  >
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                  </select>
                </div>

                {/* Skeleton Color for Bergere */}
                {design.furnitureType === 'bergere' && (
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      {t('skeletonColor')}
                    </label>
                    <select
                      className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      value={design.skeleton?.color || 'CevizAhsap'}
                      onChange={(e) => {
                        setDesign(prev => ({
                          ...prev,
                          skeleton: {
                            ...prev.skeleton!,
                            color: e.target.value
                          }
                        }));
                      }}
                    >
                      <option value="CevizAhsap">{t('CevizAhsap')}</option>
                      <option value="BeyazAhsap">{t('BeyazAhsap')}</option>
                      <option value="EkruAhsap">{t('EkruAhsap')}</option>
                      <option value="GriAhsap">{t('GriAhsap')}</option>
                      <option value="AntrasitAhsap">{t('AntrasitAhsap')}</option>
                      <option value="SariEskitmeAhsap">{t('SariEskitmeAhsap')}</option>
                      <option value="GriEskitmeAhsap">{t('GriEskitmeAhsap')}</option>
                      <option value="CevizEskitmeAhsap">{t('CevizEskitmeAhsap')}</option>
                    </select>
                  </div>
                )}

                {design.furnitureType !== 'bergere' && (
                  <div className="mt-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">{t('seatSubOptions')}</label>
                    <div className="flex flex-wrap gap-4">
                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          id="seat-option-cektirme"
                          className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                          checked={design.seat.options.cektirme}
                          onChange={() => handleSeatOptionChange('options', 'cektirme')}
                        />
                        <span className="ml-2 text-sm text-gray-700">{t('pull')}</span>
                      </label>
                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          id="seat-option-tekParca"
                          className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                          checked={design.seat.options.tekParca}
                          onChange={() => handleSeatOptionChange('options', 'tekParca')}
                        />
                        <span className="ml-2 text-sm text-gray-700">{t('singlePiece')}</span>
                      </label>
                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          id="seat-option-bombe"
                          className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                          checked={design.seat.options.bombe}
                          onChange={() => handleSeatOptionChange('options', 'bombe')}
                        />
                        <span className="ml-2 text-sm text-gray-700">{t('convex')}</span>
                      </label>
                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          id="seat-option-kirisiklik"
                          className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded opacity-50 cursor-not-allowed"
                          checked={design.seat.options.kirisiklik}
                          onChange={() => handleSeatOptionChange('options', 'kirisiklik')}
                          disabled
                        />
                        <span className="ml-2 text-sm text-gray-700">{t('wrinkle')}</span>
                      </label>

                    </div>
                  </div>
                )}
              </div>

              {/* Frame Type and Color Options - Hide for bergere */}
              {design.furnitureType !== 'bergere' && (
                <>
                  {/* Frame Type Selection */}
                  <div className="col-span-1 mt-1.5">
                    <h3 className="text-lg font-semibold mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('frameType', { defaultValue: 'Lower Frame Type' })}</h3>
                    <div>
                      <label className="block text-sm font-medium mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('selectFrameType', { defaultValue: 'Select Frame Type' })}</label>
                      <select
                        className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                        value={design.frame.type}
                        onChange={handleFrameTypeChange}
                      >
                        <option value="1">{t('frameType1', { defaultValue: 'Frame Type 1' })}</option>
                        <option value="2">{t('frameType2', { defaultValue: 'Frame Type 2' })}</option>
                        <option value="3">{t('frameType3', { defaultValue: 'Frame Type 3' })}</option>
                      </select>
                    </div>
                  </div>

                  {/* Frame Color Selection */}
                  <div className="col-span-1 mt-1.5">
                    <h3 className="text-lg font-semibold mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('woodColor')}</h3>
                    <div>
                      <label className="block text-sm font-medium mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('selectWoodColor')}</label>
                      <select
                        className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                        value={design.frame.color}
                        onChange={handleFrameColorChange}
                      >
                        <option value="CevizAhsap">{t('CevizAhsap')}</option>
                        <option value="BeyazAhsap">{t('BeyazAhsap')}</option>
                        <option value="EkruAhsap">{t('EkruAhsap')}</option>
                        <option value="GriAhsap">{t('GriAhsap')}</option>
                        <option value="AntrasitAhsap">{t('AntrasitAhsap')}</option>
                        <option value="SariEskitmeAhsap">{t('SariEskitmeAhsap')}</option>
                        <option value="GriEskitmeAhsap">{t('GriEskitmeAhsap')}</option>
                        <option value="CevizEskitmeAhsap">{t('CevizEskitmeAhsap')}</option>
                      </select>
                    </div>
                  </div>
                </>
              )}

              {/* Leg Options - Hide for bergere */}
              {design.furnitureType !== 'bergere' && (
                <div className="col-span-1 mt-1.5">
                  <h3 className="text-lg font-semibold mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('legs')}</h3>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('selectLegType')}</label>
                    <select
                      className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      value={design.leg.type}
                      onChange={handleLegTypeChange}
                    >
                      <option value="1 Ahşap">1 Ahşap</option>
                      <option value="2 Ahşap">2 Ahşap</option>
                      <option value="3 Metal">3 Metal</option>
                      <option value="4 Metal">4 Metal</option>
                      <option value="5 Ahşap">5 Ahşap</option>
                      <option value="6 Ahşap">6 Ahşap</option>
                      <option value="7 Metal">7 Metal</option>
                      <option value="8 Metal">8 Metal</option>
                      <option value="9 Metal">9 Metal</option>
                      <option value="10 Metal">10 Metal</option>
                      <option value="11 Ahşap">11 Ahşap</option>
                      <option value="12 Ahşap">12 Ahşap</option>
                      <option value="13 Ahşap">13 Ahşap</option>
                    </select>
                  </div>
                  <div className="mt-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('selectLegColor')}</label>
                    <select
                      className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      value={design.leg.color}
                      onChange={handleLegColorChange}
                    >
                      {/* Wood colors - show for wooden legs */}
                      {!design.leg.type.includes('Metal') && (
                        <>
                          <option value="CevizAhsap">{t('CevizAhsap')}</option>
                          <option value="BeyazAhsap">{t('BeyazAhsap')}</option>
                          <option value="EkruAhsap">{t('EkruAhsap')}</option>
                          <option value="GriAhsap">{t('GriAhsap')}</option>
                          <option value="AntrasitAhsap">{t('AntrasitAhsap')}</option>
                          <option value="SariEskitmeAhsap">{t('SariEskitmeAhsap')}</option>
                          <option value="GriEskitmeAhsap">{t('GriEskitmeAhsap')}</option>
                          <option value="CevizEskitmeAhsap">{t('CevizEskitmeAhsap')}</option>
                        </>
                      )}
                      {/* Metal legs - show all wooden colors except Ceviz + metal colors */}
                      {design.leg.type.includes('Metal') && (
                        <>
                          {/* Wooden colors (excluding Ceviz) */}
                          <option value="BeyazAhsap">{t('BeyazAhsap')}</option>
                          <option value="EkruAhsap">{t('EkruAhsap')}</option>
                          <option value="GriAhsap">{t('GriAhsap')}</option>
                          <option value="AntrasitAhsap">{t('AntrasitAhsap')}</option>
                          <option value="SariEskitmeAhsap">{t('SariEskitmeAhsap')}</option>
                          <option value="GriEskitmeAhsap">{t('GriEskitmeAhsap')}</option>
                          <option value="CevizEskitmeAhsap">{t('CevizEskitmeAhsap')}</option>
                          {/* Metal colors */}
                          <option value="Bronz">{t('Bronz')}</option>
                          <option value="Gold">{t('Gold')}</option>
                          <option value="nickel">{t('Nickel')}</option>
                        </>
                      )}
                    </select>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Pillows Section - Hide for bergere */}
        {design.furnitureType !== 'bergere' && (
          <div className="space-y-4 p-4 bg-white/90 backdrop-blur-sm border border-gray-100 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 mb-6">
            <h3 className="text-lg font-semibold mb-4 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('cushions')}</h3>

            {/* Grid layout for all 4 pillow types */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Back Pillow (Sırt Kırlenti) */}
              <div className="space-y-2 p-3 bg-gray-50 rounded-lg border border-gray-200">
                <h4 className="text-md font-medium text-gray-700 flex items-center gap-2">
                  {t('backPillowTitle')}
                </h4>
                <div className="space-y-2">
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">{t('fillMaterialLabel')}</label>
                    <select
                      className="w-full pl-3 pr-10 py-2 text-sm border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
                      value={pillows.backPillow.type}
                      onChange={(e) => handlePillowChange('backPillow', 'type', e.target.value)}
                    >
                      <option value="Silikon Elyaf">{t('Silikon Elyaf')}</option>
                      <option value="Sünger">{t('Sünger')}</option>
                      <option value="Yok">{t('Yok')}</option>
                    </select>
                  </div>

                  {pillows.backPillow.type !== 'Yok' && (
                    <>
                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">{t('fabricCatalogLabel')}</label>
                        <select
                          className="w-full pl-3 pr-10 py-2 text-sm border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
                          value={pillows.backPillow.fabric}
                          onChange={(e) => handlePillowChange('backPillow', 'fabric', e.target.value)}
                        >
                          <option value="Kartela 1">Kartela 1</option>
                          <option value="Kartela 2">Kartela 2</option>
                          <option value="Kartela 3">Kartela 3</option>
                          <option value="Kartela 4">Kartela 4</option>
                          <option value="Kartela 5">Kartela 5</option>
                          <option value="Kartela 6">Kartela 6</option>
                          <option value="Kartela 7">Kartela 7</option>
                          <option value="Kartela 8">Kartela 8</option>
                          <option value="Kartela 9">Kartela 9</option>
                          <option value="Kartela 10">Kartela 10</option>
                          <option value="Kartela 11">Kartela 11</option>
                          <option value="Kartela 12">Kartela 12</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">{t('colorLabel')}</label>
                        <select
                          className="w-full pl-3 pr-10 py-2 text-sm border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
                          value={pillows.backPillow.color}
                          onChange={(e) => handlePillowChange('backPillow', 'color', e.target.value)}
                        >
                          {/* Show different number of colors based on selected cartela */}
                          {pillows.backPillow.fabric === 'Kartela 12' ? (
                            Array.from({ length: 6 }, (_, i) => (
                              <option key={i + 1} value={String(i + 1)}>{t('colorOption', { number: i + 1 })}</option>
                            ))
                          ) : pillows.backPillow.fabric === 'Kartela 8' ? (
                            Array.from({ length: 10 }, (_, i) => (
                              <option key={i + 1} value={String(i + 1)}>{t('colorOption', { number: i + 1 })}</option>
                            ))
                          ) : (
                            Array.from({ length: 9 }, (_, i) => (
                              <option key={i + 1} value={String(i + 1)}>{t('colorOption', { number: i + 1 })}</option>
                            ))
                          )}
                        </select>
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">{t('quantityLabel')}</label>
                        <input
                          type="number"
                          min="0"
                          max="10"
                          className="w-full px-3 py-2 text-sm border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
                          value={pillows.backPillow.quantity}
                          onChange={(e) => handlePillowChange('backPillow', 'quantity', parseInt(e.target.value) || 1)}
                          placeholder={t('quantity')}
                        />
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Decorative Cushions (Kırlent 1, 2, 3) */}
              {(['pillow1', 'pillow2', 'pillow3'] as const).map((pillowKey, index) => {
                const pillow = pillows[pillowKey];
                return (
                  <div key={pillowKey} className="space-y-2 p-3 bg-gray-50 rounded-lg border border-gray-200">
                    <h4 className="text-md font-medium text-gray-700 flex items-center gap-2">
                      {t('decorativePillowTitle', { number: index + 1 })}
                    </h4>
                    <div className="space-y-2">
                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">{t('sizeInCm')}</label>
                        <select
                          className="w-full pl-3 pr-10 py-2 text-sm border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
                          value={pillow.cushion}
                          onChange={(e) => handlePillowChange(pillowKey, 'cushion', e.target.value)}
                        >
                          <option value="50*50">{t('sizeOption', { width: 50, height: 50 })}</option>
                          <option value="60*60">{t('sizeOption', { width: 60, height: 60 })}</option>
                          <option value="60*45">{t('sizeOption', { width: 60, height: 45 })}</option>
                          <option value="Yok">{t('Yok')}</option>
                        </select>
                      </div>

                      {pillow.cushion !== 'Yok' && (
                        <>
                          <div>
                            <label className="block text-xs font-medium text-gray-600 mb-1">{t('fabricCatalogLabel')}</label>
                            <select
                              className="w-full pl-3 pr-10 py-2 text-sm border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
                              value={pillow.fabric}
                              onChange={(e) => handlePillowChange(pillowKey, 'fabric', e.target.value)}
                            >
                              <option value="Kartela 1">Kartela 1</option>
                              <option value="Kartela 2">Kartela 2</option>
                              <option value="Kartela 3">Kartela 3</option>
                              <option value="Kartela 4">Kartela 4</option>
                              <option value="Kartela 5">Kartela 5</option>
                              <option value="Kartela 6">Kartela 6</option>
                              <option value="Kartela 7">Kartela 7</option>
                              <option value="Kartela 8">Kartela 8</option>
                              <option value="Kartela 9">Kartela 9</option>
                              <option value="Kartela 10">Kartela 10</option>
                              <option value="Kartela 11">Kartela 11</option>
                              <option value="Kartela 12">Kartela 12</option>
                            </select>
                          </div>

                          <div>
                            <label className="block text-xs font-medium text-gray-600 mb-1">{t('colorLabel')}</label>
                            <select
                              className="w-full pl-3 pr-10 py-2 text-sm border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
                              value={pillow.color}
                              onChange={(e) => handlePillowChange(pillowKey, 'color', e.target.value)}
                            >
                              {/* Show different number of colors based on selected cartela */}
                              {pillow.fabric === 'Kartela 12' ? (
                                Array.from({ length: 6 }, (_, i) => (
                                  <option key={i + 1} value={String(i + 1)}>{t('colorOption', { number: i + 1 })}</option>
                                ))
                              ) : pillow.fabric === 'Kartela 8' ? (
                                Array.from({ length: 10 }, (_, i) => (
                                  <option key={i + 1} value={String(i + 1)}>{t('colorOption', { number: i + 1 })}</option>
                                ))
                              ) : (
                                Array.from({ length: 9 }, (_, i) => (
                                  <option key={i + 1} value={String(i + 1)}>{t('colorOption', { number: i + 1 })}</option>
                                ))
                              )}
                            </select>
                          </div>

                          <div>
                            <label className="block text-xs font-medium text-gray-600 mb-1">{t('quantityLabel')}</label>
                            <input
                              type="number"
                              min="0"
                              max="10"
                              className="w-full px-3 py-2 text-sm border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
                              value={pillow.quantity}
                              onChange={(e) => handlePillowChange(pillowKey, 'quantity', parseInt(e.target.value) || 1)}
                              placeholder={t('quantity')}
                            />
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Customer Information and Action Buttons */}
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mx-4">
            <div>
              <label className="block text-sm font-medium mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('customerName')}</label>
              <input
                type="text"
                className="w-full pl-3 pr-3 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                value={customerInfo.firstName}
                onChange={(e) => handleCustomerInfoChange('firstName', e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('customerSurname')}</label>
              <input
                type="text"
                className="w-full pl-3 pr-3 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                value={customerInfo.lastName}
                onChange={(e) => handleCustomerInfoChange('lastName', e.target.value)}
              />
            </div>
          </div>

          <div className="flex justify-between items-center">
            <button
              onClick={handlePrint}
              className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-gray-100 text-gray-700 hover:bg-gray-200 h-10 py-2 px-4 font-medium py-2.5 px-4 rounded-lg transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg focus:ring-2 focus:ring-gray-300 focus:ring-offset-2"
            >
              {t('print')}
            </button>
            <div className="flex space-x-4">
              <button
                onClick={handleSaveDesign}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-white border-2 border-purple-600 text-purple-600 hover:bg-purple-50 h-10 py-2 px-4 font-medium py-2.5 px-4 rounded-lg transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg focus:ring-2 focus:ring-purple-300 focus:ring-offset-2"
              >
                {t('saveDesign')}
              </button>
              <button
                onClick={handleAddToCart}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-10 py-2 px-4 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
              >
                {t('addToCart')}
              </button>
            </div>
          </div>

          {/* Barcode and Total Price */}
          <div className="flex justify-between items-center mt-8">
            <div className="flex flex-col">
              <h3 className="text-lg font-semibold text-gray-700 mb-1">{t('barcode')}</h3>
              <div className="relative group">
                <div className="invisible group-hover:visible absolute left-0 bottom-full mb-2 p-4 bg-white rounded-lg shadow-xl border border-gray-200 z-50 w-96">
                  <h4 className="font-semibold text-gray-800 mb-2">{t('barcodeExplanation')}</h4>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-semibold">{t('fabricCode')}:</span> {t('fabricCodeDesc')}</p>
                    <p><span className="font-semibold">{t('armrestCode')}:</span> {t('armrestCodeDesc')}</p>
                    <p><span className="font-semibold">{t('seatCode')}:</span> {t('seatCodeDesc')}</p>
                    <p><span className="font-semibold">{t('baseFrameCode')}:</span> {t('baseFrameCodeDesc')}</p>
                    <p><span className="font-semibold">{t('legColorCode')}:</span> {t('legColorCodeDesc')}</p>
                    <p><span className="font-semibold">{t('woodColorCode')}:</span> {t('woodColorCodeDesc')}</p>
                  </div>
                  <div className="absolute left-4 bottom-[-8px] w-4 h-4 bg-white border-r border-b border-gray-200 transform rotate-45"></div>
                </div>
                <p className="text-base text-gray-900 font-mono bg-gray-50 px-4 py-2 rounded-md border border-gray-200 cursor-help">{barcodeText}</p>
              </div>
            </div>
            <div className="flex flex-col items-end">
              <h3 className="text-lg font-semibold text-gray-700 mb-1">{t('totalPrice')}</h3>
              <div>
                {isLoadingPrice ? (
                  <span className="text-lg text-gray-400">{t('loading')}</span>
                ) : (
                  <p className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    {formatPrice(priceBreakdown.totalPrice)}
                  </p>
                )}
              </div>

            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default FurnitureCustomizer;