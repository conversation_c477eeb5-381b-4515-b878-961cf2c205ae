import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  FiCheckCircle, FiPackage, FiArrowLeft, FiStar,
  FiInfo, FiPrinter, FiDownload, FiMail, FiUser
} from 'react-icons/fi';
import '@/styles/print.css';

const OrderConfirmation = () => {
  const { t } = useTranslation(['cart']);
  const navigate = useNavigate();
  const [orderData, setOrderData] = useState<any>(null);
  const [showDetails, setShowDetails] = useState<{ [key: string]: boolean }>({});

  useEffect(() => {
    // Get order data from localStorage or state management
    const lastOrder = localStorage.getItem('lastOrder');
    if (lastOrder) {
      const parsedData = JSON.parse(lastOrder);

      // Handle both single order and multiple orders format
      if (parsedData.orders) {
        // New format with multiple orders
        setOrderData(parsedData);
      } else {
        // Legacy format with single order
        setOrderData({
          orders: [parsedData],
          totalAmount: parsedData.totalAmount,
          orderDate: parsedData.orderDate
        });
      }
    }

    // Clear cart from localStorage after successful order
    localStorage.removeItem('cartItems');
  }, []);

  const toggleDetails = (itemId: string) => {
    setShowDetails(prev => ({ ...prev, [itemId]: !prev[itemId] }));
  };

  const formatFabricDisplay = (fabric: { option: string; color: string }) => {
    if (!fabric) return '-';

    const formatText = (text: string) => {
      return text
        .replace(/([A-Z])/g, ' $1')
        .replace(/([0-9]+)/g, ' $1')
        .trim()
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ');
    };

    return `${formatText(fabric.option)} - ${formatText(fabric.color)}`;
  };

  const calculateTotal = () => {
    if (!orderData) return 0;

    // Handle multiple orders
    if (orderData.orders) {
      return orderData.orders.reduce((total: number, order: any) => {
        const orderTotal = order.items?.reduce((sum: number, item: any) => {
          return sum + (item.totalPrice || (item.design?.totalPrice || 0) * item.quantity);
        }, 0) || 0;
        return total + orderTotal;
      }, 0);
    }

    // Legacy single order format
    if (orderData.items) {
      return orderData.items.reduce((total: number, item: any) => {
        return total + (item.design?.totalPrice || 0) * item.quantity;
      }, 0);
    }

    return orderData.totalAmount || 0;
  };

  const handlePrint = () => {
    window.print();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50">
      {/* Success Animation Background */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-20 left-20 w-64 h-64 bg-purple-200 rounded-full opacity-20 blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-pink-200 rounded-full opacity-20 blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="relative z-10 max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-green-400 to-green-600 rounded-full mb-4 animate-bounce">
            <FiCheckCircle className="w-16 h-16 text-white" />
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
            {t('orderConfirmation')}
          </h1>
          <p className="text-xl text-gray-600">
            {t('orderSuccessMessage')}
          </p>
          {orderData?.orders && orderData.orders.length > 0 && (
            <div className="mt-4">
              <p className="text-lg text-gray-700 mb-2">
                {orderData.orders.length === 1 ?
                  t('orderCreated', { defaultValue: '1 order has been created' }) :
                  t('ordersCreated', { count: orderData.orders.length, defaultValue: `${orderData.orders.length} orders have been created` })}
              </p>
              <div className="flex flex-wrap justify-center gap-2">
                {orderData.orders.map((order: any, idx: number) => (
                  <div key={idx} className="inline-flex items-center gap-2 px-4 py-2 bg-purple-100 rounded-full">
                    <span className="text-gray-700">
                      {order.customer?.name || 'Guest'} {order.customer?.surname || ''}
                    </span>
                    {order.id && (
                      <>
                        <span className="text-gray-500">•</span>
                        <span className="font-mono font-bold text-purple-700">#{order.id.substring(0, 8).toUpperCase()}</span>
                      </>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Order Details */}
        {orderData && orderData.orders && orderData.orders.length > 0 && (
          <div className="space-y-6 mb-8">
            {orderData.orders.map((order: any, orderIndex: number) => (
              <div key={orderIndex} className="bg-white rounded-3xl shadow-2xl overflow-hidden">
                <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-6">
                  <h2 className="text-2xl font-bold flex items-center gap-3">
                    <FiPackage className="w-7 h-7" />
                    {t('orderSummary')} - {order.customer?.name || 'Guest'} {order.customer?.surname || ''}
                    {order.id && (
                      <span className="text-lg font-mono ml-auto opacity-90">#{order.id.substring(0, 8).toUpperCase()}</span>
                    )}
                  </h2>
                </div>

                <div className="p-6">
                  <div className="space-y-6">
                    {(order.items || []).map((item: any, index: number) => (
                      <div key={index} className="border border-gray-200 rounded-2xl overflow-hidden hover:shadow-lg transition-all duration-300">
                        {/* Item Header */}
                        <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                              <div className="w-20 h-20 bg-gradient-to-br from-purple-400 to-pink-400 rounded-xl flex items-center justify-center">
                                <FiPackage className="w-10 h-10 text-white" />
                              </div>
                              <div>
                                <h3 className="text-xl font-bold text-gray-900">{item.design?.model || t('model')}</h3>
                                <p className="text-gray-600">{t(`furnitureTypes.${item.design?.armchairType || 'armchair'}`)}</p>
                                <div className="flex items-center gap-4 mt-2">
                                  <p className="text-sm text-gray-500">
                                    {t('barcode')}: <span className="font-mono">{item.design?.barcode || '-'}</span>
                                  </p>
                                  {item.design?.customerName && (
                                    <p className="text-sm text-purple-600 font-medium bg-purple-50 px-2 py-1 rounded">
                                      <FiUser className="inline w-3 h-3 mr-1" />
                                      {item.design.customerName} {item.design.customerSurname || ''}
                                    </p>
                                  )}
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="text-sm text-gray-600">{t('quantity')}: {item.quantity}</p>
                              <p className="text-2xl font-bold text-purple-600">
                                ₺{((item.design?.totalPrice || 0) * item.quantity).toLocaleString('tr-TR', {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2
                                })}
                              </p>
                            </div>
                          </div>

                          <button
                            onClick={() => toggleDetails(`item-${index}`)}
                            className="mt-4 w-full py-2 px-4 bg-purple-100 hover:bg-purple-200 text-purple-700 rounded-lg font-medium transition-all duration-300"
                          >
                            {showDetails[`item-${index}`] ? t('hideDetails', { defaultValue: 'Hide Details' }) : t('showDetails', { defaultValue: 'Show Details' })}
                          </button>
                        </div>

                        {/* Item Details */}
                        {showDetails[`item-${index}`] && (
                          <div className="p-6 bg-gray-50 border-t border-gray-200">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                              {/* Structure */}
                              <div className="bg-white p-4 rounded-xl shadow-sm">
                                <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                                  <FiInfo className="w-5 h-5 text-blue-500" />
                                  {t('structure', { defaultValue: 'Structure' })}
                                </h4>
                                <div className="space-y-2 text-sm">
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">{t('legs')}:</span>
                                    <span className="font-medium">{item.design?.legs || '-'}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">{t('baseFrame')}:</span>
                                    <span className="font-medium">{item.design?.baseFrame || '-'}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">{t('woodVeneer')}:</span>
                                    <span className="font-medium">{item.design?.woodVeneer || '-'}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">{t('backPillow')}:</span>
                                    <span className="font-medium">{item.design?.backPillow || '-'}</span>
                                  </div>
                                </div>
                              </div>

                              {/* Fabrics */}
                              <div className="bg-white p-4 rounded-xl shadow-sm">
                                <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                                  <FiStar className="w-5 h-5 text-purple-500" />
                                  {t('fabricDetails')}
                                </h4>
                                <div className="space-y-2 text-sm">
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">{t('sharedFabric')}:</span>
                                    <span className="font-medium text-right">{formatFabricDisplay(item.design?.sharedFabric)}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">{t('woodVeneerFabric')}:</span>
                                    <span className="font-medium text-right">{formatFabricDisplay(item.design?.woodVeneerFabric)}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">{t('legFabric')}:</span>
                                    <span className="font-medium text-right">{formatFabricDisplay(item.design?.legFabric)}</span>
                                  </div>
                                </div>
                              </div>

                              {/* Armrest & Seat */}
                              <div className="bg-white p-4 rounded-xl shadow-sm">
                                <h4 className="font-semibold text-gray-900 mb-3">{t('armrest')}</h4>
                                <div className="space-y-2 text-sm">
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">{t('main', { defaultValue: 'Main' })}:</span>
                                    <span className="font-medium">{item.design?.armrest?.main || '-'}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">{t('sub', { defaultValue: 'Sub' })}:</span>
                                    <span className="font-medium">{item.design?.armrest?.sub || '-'}</span>
                                  </div>
                                </div>
                              </div>

                              <div className="bg-white p-4 rounded-xl shadow-sm">
                                <h4 className="font-semibold text-gray-900 mb-3">{t('seat')}</h4>
                                <div className="space-y-2 text-sm">
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">{t('option', { defaultValue: 'Option' })}:</span>
                                    <span className="font-medium">{item.design?.seat?.option || '-'}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">{t('sub', { defaultValue: 'Sub' })}:</span>
                                    <span className="font-medium">{item.design?.seat?.sub || '-'}</span>
                                  </div>
                                </div>
                              </div>

                              {/* Cushions */}
                              {item.design?.cushions && item.design.cushions.length > 0 && (
                                <div className="md:col-span-2 bg-white p-4 rounded-xl shadow-sm">
                                  <h4 className="font-semibold text-gray-900 mb-3">{t('cushions')} ({item.design.cushions.length})</h4>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                    {item.design.cushions.map((cushion: any, cIndex: number) => (
                                      <div key={cIndex} className="text-sm bg-gray-50 p-2 rounded">
                                        <span className="text-gray-600">{t('cushion', { defaultValue: 'Cushion' })} {cIndex + 1}:</span>
                                        <span className="font-medium ml-2">
                                          {cushion.cushionType} • {cushion.fabric} • {cushion.color}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Order Total */}
                  <div className="mt-6 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-semibold text-gray-900">{t('orderTotal', { defaultValue: 'Order Total' })}</span>
                      <span className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                        ${(order.totalAmount || order.items?.reduce((sum: number, item: any) =>
                          sum + (item.totalPrice || (item.design?.totalPrice || 0) * item.quantity), 0) || 0
                        ).toLocaleString('en-US', {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2
                        })}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Grand Total */}
            <div className="bg-white rounded-3xl shadow-2xl overflow-hidden p-6">
              <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6">
                <div className="flex justify-between items-center">
                  <span className="text-xl font-semibold text-gray-900">{t('grandTotal', { defaultValue: 'Grand Total' })}</span>
                  <span className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    ₺{calculateTotal().toLocaleString('tr-TR', {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2
                    })}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Customer Information */}
        {orderData && orderData.orders && orderData.orders.length > 0 && (
          <div className="bg-white rounded-3xl shadow-xl p-6 mb-8">
            <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <FiInfo className="w-6 h-6 text-purple-600" />
              {t('customerInformation', { defaultValue: 'Customer Information' })}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {orderData.orders.map((order: any, idx: number) => (
                <div key={idx} className="bg-gray-50 p-4 rounded-xl">
                  <p className="text-sm text-gray-600 mb-1">
                    {t('customer')} {orderData.orders.length > 1 ? `${idx + 1}` : ''}
                  </p>
                  <p className="font-semibold text-gray-900">
                    {order.customer?.name || '-'} {order.customer?.surname || '-'}
                  </p>
                  {order.id && (
                    <p className="text-xs text-gray-500 mt-1 font-mono">
                      Order #{order.id.substring(0, 8).toUpperCase()}
                    </p>
                  )}
                </div>
              ))}
              <div className="bg-gray-50 p-4 rounded-xl">
                <p className="text-sm text-gray-600 mb-1">{t('orderDate', { defaultValue: 'Order Date' })}</p>
                <p className="font-semibold text-gray-900">
                  {new Date(orderData.orderDate || new Date()).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Sales Contract Note */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 mb-8">
          <div className="flex items-start gap-3">
            <FiInfo className="w-6 h-6 text-blue-600 flex-shrink-0 mt-1" />
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">{t('salesContract')}</h4>
              <p className="text-sm text-gray-700 leading-relaxed">
                {t('salesContractText')}
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <button
            onClick={handlePrint}
            className="flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-bold text-lg rounded-2xl hover:from-green-700 hover:to-emerald-700 transform hover:scale-105 transition-all duration-300 shadow-2xl print:hidden"
          >
            <FiPrinter className="w-6 h-6" />
            {t('printOrder', { defaultValue: 'Print Order' })}
          </button>
          <button
            onClick={() => navigate('/')}
            className="flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-bold text-lg rounded-2xl hover:from-purple-700 hover:to-pink-700 transform hover:scale-105 transition-all duration-300 shadow-2xl print:hidden"
          >
            <FiArrowLeft className="w-6 h-6" />
            {t('backToHome')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default OrderConfirmation;